#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分配引擎模块
负责数据分配算法的实现
"""

import pandas as pd
from utils.helpers import get_available_staff


class AllocationEngine:
    """数据分配引擎"""
    
    def __init__(self, status_callback=None):
        self.status_callback = status_callback
    
    def emit_status(self, message):
        """发送状态消息"""
        if self.status_callback:
            self.status_callback(message)
    
    def check_and_handle_excess_data(self, operation_code, op_info, daily_count, unmatched_data):
        """
        检查运营表格数据量是否超过每日工作总表数量，如果超过则将多余部分移至未匹配数据
        
        Args:
            operation_code: 运营编码
            op_info: 运营表格信息
            daily_count: 每日工作总表中该编码的数据量
            unmatched_data: 未匹配数据列表，用于存放多余数据
            
        Returns:
            bool: 是否有数据被移至未匹配
        """
        # 先过滤空白行
        data_rows = op_info['data_rows']
        
        # 判断标准：如果一行中所有单元格都是空或NaN或只包含空格，则认为是空白行
        non_empty_rows = data_rows.apply(lambda row: not all(
            pd.isna(val) or (isinstance(val, str) and val.strip() == '') 
            for val in row
        ), axis=1)
        
        # 只保留非空行
        filtered_data_rows = data_rows[non_empty_rows]
        
        # 如果过滤掉了空白行，记录日志并更新数据
        empty_rows_count = len(data_rows) - len(filtered_data_rows)
        if empty_rows_count > 0:
            self.emit_status(f"🧹 运营编码 '{operation_code}': 过滤掉 {empty_rows_count} 行空白数据")
            op_info['data_rows'] = filtered_data_rows
            op_info['_data_count'] = len(filtered_data_rows)
        
        # 获取运营表格中该编码的数据量（已过滤空白行）
        operation_data_count = len(op_info['data_rows'])
        
        # 检查每日工作总表数据量是否足够
        if daily_count < operation_data_count and daily_count > 0:
            self.emit_status(f"⚠️ 运营编码 '{operation_code}' 每日工作数据量不足: {daily_count} < {operation_data_count}")
            self.emit_status(f"⚠️ 请确保每日工作总表中的编码数量充足，当前数据将被保留但可能导致分配不平衡")
            
        # 如果运营表格数据量大于每日工作总表数据量
        if operation_data_count > daily_count and daily_count > 0:
            self.emit_status(f"⚖️ 运营编码 '{operation_code}' 数据量超出限制: {operation_data_count} → {daily_count}")
            
            # 保留与每日工作总表数量相同的数据
            kept_data = op_info['data_rows'].iloc[:daily_count].copy()
            
            # 将多余的数据移至未匹配数据
            excess_data = op_info['data_rows'].iloc[daily_count:].copy()
            excess_count = len(excess_data)
            
            # 将多余数据转换为字典格式并添加分析信息
            for _, row in excess_data.iterrows():
                unmatched_item = row.to_dict()
                
                # 添加分析信息
                unmatched_item.update({
                    '运营编码': operation_code,
                    '未匹配原因': '超出每日工作总表数据量',
                    '详细分析': f'运营表格数据量({operation_data_count})超过每日工作总表数据量({daily_count})',
                    '建议解决方案': '检查每日工作总表是否完整，或确认运营表格是否包含多余数据',
                    '原始文件': op_info.get('file_path', '未知'),
                    '文件名': op_info.get('file_name', '未知'),  # 添加文件名，用于提取店铺名称
                    '数据行号': _ + 3,  # 假设数据从第3行开始
                    '分析时间': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
                })
                
                unmatched_data.append(unmatched_item)
            
            # 更新运营表格数据，只保留与每日工作总表数量相同的数据
            op_info['data_rows'] = kept_data
            op_info['_data_count'] = len(kept_data)
            
            self.emit_status(f"📤 已将 {excess_count} 条多余数据移至未匹配数据分析报告")
            return True
            
        return False
    
    def analyze_phone_operation_mapping(self, daily_work_df, operation_data, unmatched_data=None):
        """分析每日工作总表中手机名称和运营编码的关系，并按数据量分配"""
        phone_operation_mapping = {}

        # 检查每日工作总表是否有必要的列
        if daily_work_df is None or len(daily_work_df) == 0:
            self.emit_status("每日工作总表为空，跳过手机分配分析")
            return phone_operation_mapping

        required_columns = ['手机名称', '运营编码']
        missing_columns = [col for col in required_columns if col not in daily_work_df.columns]

        if missing_columns:
            self.emit_status(f"每日工作总表缺少列: {missing_columns}，跳过手机分配分析")
            return phone_operation_mapping
            
        # 初始化未匹配数据列表（如果未提供）
        if unmatched_data is None:
            unmatched_data = []

        # 建立每日工作总表编码到运营表格编码的映射（支持二次匹配）
        daily_to_operation_mapping = {}
        for op_code, op_info in operation_data.items():
            if 'secondary_match' in op_info:
                # 二次匹配的情况：每日工作总表编码 -> 运营表格编码
                if 'all_matches' in op_info['secondary_match']:
                    # 有多个匹配，为每个匹配建立映射
                    for daily_code in op_info['secondary_match']['all_matches']:
                        daily_to_operation_mapping[daily_code] = op_code
                        self.emit_status(f"二次匹配映射: 每日工作编码 '{daily_code}' -> 运营编码 '{op_code}'")
                else:
                    # 只有一个匹配
                    daily_code = op_info['secondary_match']['matched_code']
                    daily_to_operation_mapping[daily_code] = op_code
                    self.emit_status(f"二次匹配映射: 每日工作编码 '{daily_code}' -> 运营编码 '{op_code}'")
            else:
                # 直接匹配的情况
                daily_to_operation_mapping[op_code] = op_code

        # 预处理手机名称，处理可能包含多个手机的情况
        processed_daily_work_df = daily_work_df.copy()
        
        # 创建一个函数来处理手机名称字段
        def process_phone_name(phone_name):
            if not isinstance(phone_name, str):
                return phone_name
            # 替换中文逗号为英文逗号
            phone_name = phone_name.replace('，', ',')
            # 如果包含逗号或空格，表示多个手机，取第一个
            if ',' in phone_name or ' ' in phone_name:
                phone_name = phone_name.replace(' ', ',')
                parts = [p.strip() for p in phone_name.split(',') if p.strip()]
                if parts:
                    return parts[0]
            return phone_name.strip()
        
        # 应用处理函数
        processed_daily_work_df['手机名称_处理后'] = processed_daily_work_df['手机名称'].apply(process_phone_name)
        
        # 输出处理前后的手机名称，帮助调试
        for _, row in processed_daily_work_df.iterrows():
            original_name = str(row['手机名称']).strip()
            processed_name = str(row['手机名称_处理后']).strip()
            if original_name != processed_name:
                self.emit_status(f"手机名称处理: '{original_name}' -> '{processed_name}'")
        
        # 统计每个运营编码对应的手机名称和数量
        operation_stats = {}
        for _, row in processed_daily_work_df.iterrows():
            daily_operation_code = str(row['运营编码']).strip()
            phone_name = str(row['手机名称_处理后']).strip()

            # 检查是否有对应的运营表格数据
            if daily_operation_code in daily_to_operation_mapping:
                # 使用运营表格中的编码作为键
                operation_code = daily_to_operation_mapping[daily_operation_code]

                if operation_code not in operation_stats:
                    operation_stats[operation_code] = {}

                if phone_name not in operation_stats[operation_code]:
                    operation_stats[operation_code][phone_name] = 0
                operation_stats[operation_code][phone_name] += 1

        # 处理每个运营编码
        for operation_code, phone_counts in operation_stats.items():
            if operation_code not in operation_data:
                continue  # 跳过已经被处理或删除的运营编码
                
            # 获取每日工作总表中该运营编码的总数据量
            total_daily_count = sum(phone_counts.values())
            
            # 检查运营表格数据量是否超过每日工作总表数据量，如果超过则处理多余数据
            self.check_and_handle_excess_data(operation_code, operation_data[operation_code], total_daily_count, unmatched_data)
            
            # 输出该运营编码对应的所有手机名称和数量
            self.emit_status(f"运营编码 '{operation_code}' 对应的手机名称统计:")
            for phone, count in phone_counts.items():
                self.emit_status(f"  - 手机名称: '{phone}', 数据量: {count}")
            
            if len(phone_counts) == 1:
                # 运营编码只对应一个手机，正常处理
                phone_name = list(phone_counts.keys())[0]
                data_count = phone_counts[phone_name]

                if phone_name not in phone_operation_mapping:
                    phone_operation_mapping[phone_name] = set()
                phone_operation_mapping[phone_name].add(operation_code)

                self.emit_status(f"✅ 运营编码 '{operation_code}' 分配给手机 '{phone_name}'，数据量: {data_count}")

            else:
                # 运营编码对应多个手机，需要按数据量分割
                self.emit_status(f"⚠️ 运营编码 '{operation_code}' 对应多个手机，按数据量分割:")

                original_data = operation_data[operation_code]
                total_daily_count = sum(phone_counts.values())
                available_data_count = len(original_data['data_rows'])

                self.emit_status(f"  每日工作总表数据量: {total_daily_count}, 运营表格数据量: {available_data_count}")

                # 按比例分配数据
                start_index = 0
                for i, (phone_name, daily_count) in enumerate(phone_counts.items()):
                    # 计算这个手机应该分配的数据量
                    if i == len(phone_counts) - 1:
                        # 最后一个手机分配剩余的所有数据
                        allocated_count = available_data_count - start_index
                    else:
                        # 按比例分配
                        ratio = daily_count / total_daily_count
                        allocated_count = min(int(available_data_count * ratio), daily_count)

                    if allocated_count > 0:
                        # 创建新的运营编码标识
                        new_operation_code = f"{operation_code}_手机_{phone_name}"

                        # 分割数据
                        end_index = start_index + allocated_count
                        allocated_data_rows = original_data['data_rows'].iloc[start_index:end_index].copy()

                        # 创建新的运营数据
                        operation_data[new_operation_code] = {
                            'file_name': original_data['file_name'],
                            'file_path': original_data['file_path'],
                            'header_rows': original_data['header_rows'].copy(),
                            'data_rows': allocated_data_rows,
                            'original_df': original_data['original_df']
                        }

                        # 添加到手机映射
                        if phone_name not in phone_operation_mapping:
                            phone_operation_mapping[phone_name] = set()
                        phone_operation_mapping[phone_name].add(new_operation_code)

                        self.emit_status(f"  创建 '{new_operation_code}' 分配给 '{phone_name}'，数据量: {allocated_count}")
                        start_index = end_index

                # 删除原始的运营编码数据
                del operation_data[operation_code]

        # 输出最终分析结果
        self.emit_status(f"手机分配分析完成:")
        for phone_name, operation_codes in phone_operation_mapping.items():
            total_count = 0
            for code in operation_codes:
                if code in operation_data:
                    total_count += len(operation_data[code]['data_rows'])
            self.emit_status(f"  📱 {phone_name}: {len(operation_codes)} 个运营编码，总数据量: {total_count}")

        return phone_operation_mapping

    def extract_digits_from_code(self, code):
        """
        从运营编码中提取纯数字部分

        对于带后缀的编码（如 ZDDLLS-3740581615068381598-B1），
        只提取主体部分的数字（3740581615068381598），
        不包括后缀中的数字（B1中的1）
        """
        import re
        if not code:
            return ""

        code = str(code).strip()

        # 尝试匹配标准运营编码格式：PREFIX-MAINCODE-SUFFIX
        # 其中 SUFFIX 可能是 A1, B1, C2 等字母+数字组合
        match = re.match(r'^([A-Z]+)-([A-Z0-9]+)-([A-Z][0-9]*)$', code)
        if match:
            # 如果匹配到标准格式，只从主体部分提取数字
            main_code = match.group(2)
            digits = re.findall(r'\d', main_code)
            return ''.join(digits)

        # 尝试匹配无后缀格式：PREFIX-MAINCODE
        match = re.match(r'^([A-Z]+)-([A-Z0-9]+)$', code)
        if match:
            # 如果匹配到无后缀格式，从主体部分提取数字
            main_code = match.group(2)
            digits = re.findall(r'\d', main_code)
            return ''.join(digits)

        # 如果不匹配标准格式，回退到原来的逻辑（提取所有数字）
        digits = re.findall(r'\d', code)
        return ''.join(digits)
        
    def allocate_by_phone_groups(self, operation_data, phone_operation_mapping, available_staff,
                               allocated_data, staff_operation_codes, unmatched_data, daily_work_df=None, phone_to_staff=None):
        """按手机分组进行分配 - 考虑数据量平衡"""
        allocated_operations = set()  # 记录已分配的运营编码
        
        # 初始化手机到客服的映射（如果未提供）
        if phone_to_staff is None:
            phone_to_staff = {}
            
        # 输出所有手机分组信息，帮助调试
        self.emit_status("📊 手机分组信息:")
        for phone_name, operation_codes in phone_operation_mapping.items():
            self.emit_status(f"  📱 手机 '{phone_name}': {len(operation_codes)} 个运营编码 - {sorted(list(operation_codes))}")

        # 计算每个手机组的数据量
        phone_data_counts = {}
        for phone_name, operation_codes in phone_operation_mapping.items():
            total_count = 0
            for code in operation_codes:
                if code in operation_data:
                    total_count += len(operation_data[code]['data_rows'])
            phone_data_counts[phone_name] = total_count
            self.emit_status(f"  📊 手机 '{phone_name}' 总数据量: {total_count}")

        # 按数据量排序手机组（从大到小）
        sorted_phones = sorted(phone_data_counts.items(), key=lambda x: x[1], reverse=True)
        self.emit_status("📊 手机分组按数据量排序:")
        for phone_name, count in sorted_phones:
            self.emit_status(f"  📱 手机 '{phone_name}': {count} 条数据")

        # 初始化客服的数据量统计
        staff_data_counts = {staff: 0 for staff in available_staff}

        self.emit_status(f"📱 开始按手机分组分配数据，共 {len(sorted_phones)} 个手机组")

        # 预先检查所有运营编码的每日工作数据量是否足够
        if daily_work_df is not None and '运营编码' in daily_work_df.columns:
            self.emit_status("检查每日工作总表数据量是否足够...")
            daily_code_counts = {}
            
            # 获取每日工作总表中每个运营编码的数据量
            for operation_code, op_info in operation_data.items():
                # 查找每日工作总表中该运营编码的数据量
                daily_codes = self.get_daily_work_codes_for_operation(operation_code, operation_data)
                
                # 提取纯数字编码用于匹配
                digit_codes = set()
                for daily_code in daily_codes:
                    digit_code = self.extract_digits_from_code(daily_code)
                    if digit_code:
                        digit_codes.add(digit_code)
                
                # 同时使用原始编码和纯数字编码进行匹配
                daily_data = daily_work_df[
                    (daily_work_df['运营编码'].isin(daily_codes)) | 
                    (daily_work_df['运营编码'].astype(str).apply(self.extract_digits_from_code).isin(digit_codes))
                ]
                daily_count = len(daily_data)
                daily_code_counts[operation_code] = daily_count
                
                # 获取运营表格中该编码的数据量
                operation_data_count = len(op_info['data_rows'])
                
                # 检查每日工作总表数据量是否足够
                if daily_count < operation_data_count and daily_count > 0:
                    self.emit_status(f"⚠️ 运营编码 '{operation_code}' 每日工作数据量不足: {daily_count} < {operation_data_count}")
                    self.emit_status(f"⚠️ 请确保每日工作总表中的编码数量充足，当前数据将被保留但可能导致分配不平衡")

        for phone_name, _ in sorted_phones:
            operation_codes = phone_operation_mapping[phone_name]
            
            # 检查这个手机是否已经分配给某个客服
            if phone_name in phone_to_staff:
                # 如果已分配，使用之前分配的客服
                assigned_staff = phone_to_staff[phone_name]
                self.emit_status(f"📱 手机 '{phone_name}' 已分配给客服 '{assigned_staff}'，保持一致性")
            else:
                # 如果未分配，选择当前数据量最少的客服
                assigned_staff = min(staff_data_counts.keys(), key=lambda x: staff_data_counts[x])
                # 记录手机分配给了哪个客服
                phone_to_staff[phone_name] = assigned_staff
                self.emit_status(f"📱 手机 '{phone_name}' 新分配给客服 '{assigned_staff}'")

            # 过滤掉已经分配的运营编码
            unallocated_codes = [code for code in operation_codes if code not in allocated_operations]

            if unallocated_codes:
                self.emit_status(f"📱 手机 '{phone_name}' → 客服 '{assigned_staff}'，{len(unallocated_codes)} 个运营编码")

                for operation_code in unallocated_codes:
                    if operation_code in operation_data:
                        op_info = operation_data[operation_code]
                        
                        # 获取每日工作总表中该运营编码的数据量
                        if daily_work_df is not None and '运营编码' in daily_work_df.columns:
                            # 查找每日工作总表中该运营编码的数据量
                            daily_codes = self.get_daily_work_codes_for_operation(operation_code, operation_data)
                            
                            # 提取纯数字编码用于匹配
                            digit_codes = set()
                            for daily_code in daily_codes:
                                digit_code = self.extract_digits_from_code(daily_code)
                                if digit_code:
                                    digit_codes.add(digit_code)
                            
                            # 同时使用原始编码和纯数字编码进行匹配
                            daily_data = daily_work_df[
                                (daily_work_df['运营编码'].isin(daily_codes)) | 
                                (daily_work_df['运营编码'].astype(str).apply(self.extract_digits_from_code).isin(digit_codes))
                            ]
                            daily_count = len(daily_data)
                            
                            # 检查并处理超出数据量
                            if daily_count > 0:  # 只有在找到每日工作数据时才检查
                                self.check_and_handle_excess_data(operation_code, op_info, daily_count, unmatched_data)
                        
                        data_rows = op_info['data_rows']

                        if len(data_rows) > 0:
                            # 该手机的所有运营编码和数据都完整分配给同一个客服（不轮询）
                            complete_table = pd.concat([
                                op_info['header_rows'],
                                data_rows  # 所有数据行都分配给这个客服
                            ], ignore_index=True)

                            sheet_name = f"{operation_code}_{assigned_staff}"
                            allocated_data[sheet_name] = {
                                'data': complete_table,
                                'operation_code': operation_code,
                                'staff_name': assigned_staff,
                                'row_count': len(data_rows),  # 所有数据行的数量
                                'file_path': op_info['file_path'],
                                'file_name': op_info['file_name'],  # 添加文件名
                                'original_df': op_info['original_df'],
                                'phone_name': phone_name if phone_name else ""  # 只有在手机名称不为空时才记录
                            }

                            staff_operation_codes[assigned_staff].add(operation_code)
                            allocated_operations.add(operation_code)  # 标记为已分配

                            # 更新客服的数据量统计
                            staff_data_counts[assigned_staff] += len(data_rows)
                            self.emit_status(f"  ✅ 运营编码 '{operation_code}' 分配给客服 '{assigned_staff}'，数据量: {len(data_rows)}")

        # 输出最终的数据量分配统计
        self.emit_status(f"📊 手机分组分配完成，客服数据量统计:")
        for staff, count in staff_data_counts.items():
            self.emit_status(f"  👤 {staff}: {count} 条数据")

        # 处理没有在手机分组中的运营编码（使用传统方式分配）
        ungrouped_operations = set(operation_data.keys()) - allocated_operations
        if ungrouped_operations:
            self.emit_status(f"⚠️ 发现 {len(ungrouped_operations)} 个未分组的运营编码，使用轮询方式分配")
            ungrouped_data = {code: operation_data[code] for code in ungrouped_operations}
            self.allocate_by_round_robin(ungrouped_data, available_staff,
                                       allocated_data, staff_operation_codes, unmatched_data, daily_work_df, phone_to_staff)

    def allocate_by_round_robin(self, operation_data, available_staff,
                              allocated_data, staff_operation_codes, unmatched_data, daily_work_df=None, phone_to_staff=None):
        """平衡分配方式 - 考虑数据量平衡 - 性能优化版本"""
        # 初始化客服的数据量统计
        staff_data_counts = {staff: 0 for staff in available_staff}
        
        # 初始化手机到客服的映射（如果未提供）
        if phone_to_staff is None:
            phone_to_staff = {}

        # 性能优化：预先计算已分配的运营编码集合
        already_allocated_codes = set()
        for codes in staff_operation_codes.values():
            already_allocated_codes.update(codes)

        # 计算每个运营编码的数据量并排序（从大到小）
        operation_data_counts = []
        for operation_code, op_info in operation_data.items():
            # 使用集合查找，比循环快
            if operation_code in already_allocated_codes:
                continue

            # 使用缓存的数据行数
            data_count = op_info.get('_data_count', len(op_info['data_rows']))
            if data_count == 0:
                continue

            operation_data_counts.append((operation_code, data_count, op_info))

        # 按数据量排序（从大到小）
        operation_data_counts.sort(key=lambda x: x[1], reverse=True)

        self.emit_status(f"🔄 开始轮询分配 {len(operation_data_counts)} 个运营编码")

        # 预先检查所有运营编码的每日工作数据量是否足够
        if daily_work_df is not None and '运营编码' in daily_work_df.columns:
            self.emit_status("检查每日工作总表数据量是否足够...")
            daily_code_counts = {}
            
            for operation_code, data_count, op_info in operation_data_counts:
                # 查找每日工作总表中该运营编码的数据量
                daily_codes = self.get_daily_work_codes_for_operation(operation_code, operation_data)
                
                # 提取纯数字编码用于匹配
                digit_codes = set()
                for daily_code in daily_codes:
                    digit_code = self.extract_digits_from_code(daily_code)
                    if digit_code:
                        digit_codes.add(digit_code)
                
                # 同时使用原始编码和纯数字编码进行匹配
                daily_data = daily_work_df[
                    (daily_work_df['运营编码'].isin(daily_codes)) | 
                    (daily_work_df['运营编码'].astype(str).apply(self.extract_digits_from_code).isin(digit_codes))
                ]
                daily_count = len(daily_data)
                daily_code_counts[operation_code] = daily_count
                
                # 检查每日工作总表数据量是否足够
                if daily_count < data_count and daily_count > 0:
                    self.emit_status(f"⚠️ 运营编码 '{operation_code}' 每日工作数据量不足: {daily_count} < {data_count}")
                    self.emit_status(f"⚠️ 请确保每日工作总表中的编码数量充足，当前数据将被保留但可能导致分配不平衡")

        for operation_code, data_count, op_info in operation_data_counts:
            if available_staff:
                # 尝试从每日工作总表中获取手机名称
                phone_name = self.get_phone_name_from_daily_work(operation_code, daily_work_df, operation_data)
                
                # 检查这个手机是否已经分配给某个客服
                if phone_name and phone_name in phone_to_staff:
                    # 如果已分配，使用之前分配的客服
                    assigned_staff = phone_to_staff[phone_name]
                    self.emit_status(f"📱 手机 '{phone_name}' 已分配给客服 '{assigned_staff}'，保持一致性")
                else:
                    # 如果未分配或没有手机名称，选择当前数据量最少的客服
                    assigned_staff = min(staff_data_counts.keys(), key=lambda x: staff_data_counts[x])
                    # 如果有手机名称，记录手机分配给了哪个客服
                    if phone_name:
                        phone_to_staff[phone_name] = assigned_staff

                # 获取每日工作总表中该运营编码的数据量
                if daily_work_df is not None and '运营编码' in daily_work_df.columns:
                    # 查找每日工作总表中该运营编码的数据量
                    daily_codes = self.get_daily_work_codes_for_operation(operation_code, operation_data)
                    
                    # 提取纯数字编码用于匹配
                    digit_codes = set()
                    for daily_code in daily_codes:
                        digit_code = self.extract_digits_from_code(daily_code)
                        if digit_code:
                            digit_codes.add(digit_code)
                    
                    # 同时使用原始编码和纯数字编码进行匹配
                    daily_data = daily_work_df[
                        (daily_work_df['运营编码'].isin(daily_codes)) | 
                        (daily_work_df['运营编码'].astype(str).apply(self.extract_digits_from_code).isin(digit_codes))
                    ]
                    daily_count = len(daily_data)
                    
                    # 检查并处理超出数据量
                    if daily_count > 0:  # 只有在找到每日工作数据时才检查
                        self.check_and_handle_excess_data(operation_code, op_info, daily_count, unmatched_data)

                # 所有数据都分配给这个客服
                complete_table = pd.concat([
                    op_info['header_rows'],
                    op_info['data_rows']  # 所有数据行都分配给这个客服
                ], ignore_index=True)

                sheet_name = f"{operation_code}_{assigned_staff}"
                allocated_data[sheet_name] = {
                    'data': complete_table,
                    'operation_code': operation_code,
                    'staff_name': assigned_staff,
                    'row_count': data_count,  # 所有数据行的数量
                    'file_path': op_info['file_path'],
                    'file_name': op_info['file_name'],  # 添加文件名
                    'original_df': op_info['original_df'],
                    'phone_name': phone_name if phone_name else ""  # 只有在手机名称不为空时才记录
                }

                staff_operation_codes[assigned_staff].add(operation_code)
                staff_data_counts[assigned_staff] += data_count

                phone_info = f"，手机: {phone_name}" if phone_name else ""
                self.emit_status(f"📄 运营编码 '{operation_code}' → 客服 '{assigned_staff}'，数据量: {data_count}{phone_info}")
            else:
                # 如果没有在班客服，将数据标记为未分配
                unmatched_data.extend(op_info['data_rows'].to_dict('records'))
                self.emit_status(f"⚠️ 运营编码 '{operation_code}' 无可用客服，已移至未匹配数据")

        # 输出最终的数据量分配统计
        if staff_data_counts:
            self.emit_status(f"📊 轮询分配完成，客服数据量统计:")
            for staff, count in staff_data_counts.items():
                self.emit_status(f"  👤 {staff}: {count} 条数据")

    def get_phone_name_from_daily_work(self, operation_code, daily_work_df, operation_data=None):
        """从每日工作总表中获取运营编码对应的手机名称，支持二次匹配和增强的数字匹配"""
        if daily_work_df is None or len(daily_work_df) == 0:
            self.emit_status(f"⚠️ 运营编码 '{operation_code}' 无法获取手机名称: 每日工作总表为空")
            return ""  # 修改为返回空字符串而不是错误信息

        # 检查必要的列是否存在
        if '运营编码' not in daily_work_df.columns or '手机名称' not in daily_work_df.columns:
            self.emit_status(f"⚠️ 运营编码 '{operation_code}' 无法获取手机名称: 每日工作总表缺少必要列")
            return ""  # 修改为返回空字符串而不是错误信息
            
        # 预处理手机名称，处理可能包含多个手机的情况
        processed_daily_work_df = daily_work_df.copy()
        
        # 创建一个函数来处理手机名称字段
        def process_phone_name(phone_name):
            if not isinstance(phone_name, str):
                return phone_name
            # 替换中文逗号为英文逗号
            phone_name = phone_name.replace('，', ',')
            # 如果包含逗号或空格，表示多个手机，取第一个
            if ',' in phone_name or ' ' in phone_name:
                phone_name = phone_name.replace(' ', ',')
                parts = [p.strip() for p in phone_name.split(',') if p.strip()]
                if parts:
                    return parts[0]
            return phone_name.strip()
        
        # 应用处理函数
        processed_daily_work_df['手机名称_处理后'] = processed_daily_work_df['手机名称'].apply(process_phone_name)

        # 检查是否是二次匹配的编码
        search_codes = [operation_code]  # 默认使用原始编码进行查找
        if operation_data and operation_code in operation_data:
            op_info = operation_data[operation_code]
            if 'secondary_match' in op_info:
                # 这是一个二次匹配的编码，使用匹配到的完整编码进行查找
                if 'all_matches' in op_info['secondary_match']:
                    # 有多个匹配，使用所有匹配进行查找
                    search_codes = op_info['secondary_match']['all_matches']
                    self.emit_status(f"使用所有二次匹配编码查找手机名称: '{operation_code}' -> {search_codes}")
                else:
                    # 只有一个匹配
                    search_codes = [op_info['secondary_match']['matched_code']]
                    self.emit_status(f"使用二次匹配编码查找手机名称: '{operation_code}' -> '{search_codes[0]}'")

        # 使用所有search_codes进行查找，合并结果
        all_matching_rows = []
        
        # 第一步：使用完整编码进行精确匹配
        for search_code in search_codes:
            matching_rows = processed_daily_work_df[processed_daily_work_df['运营编码'].astype(str).str.strip() == search_code]
            if len(matching_rows) > 0:
                all_matching_rows.append(matching_rows)
                self.emit_status(f"精确匹配找到 {len(matching_rows)} 行数据: '{operation_code}' -> '{search_code}'")
        
        # 第二步：如果精确匹配没有结果，尝试使用纯数字部分进行匹配
        if not all_matching_rows:
            # 提取运营编码的纯数字部分
            operation_digits = self.extract_digits_from_code(operation_code)
            if operation_digits and len(operation_digits) >= 6:  # 至少需要6位数字才有意义
                # 为每日工作总表中的所有运营编码提取纯数字部分
                processed_daily_work_df['运营编码_纯数字'] = processed_daily_work_df['运营编码'].astype(str).apply(self.extract_digits_from_code)
                
                # 使用纯数字部分进行匹配
                digit_matching_rows = processed_daily_work_df[processed_daily_work_df['运营编码_纯数字'] == operation_digits]
                if len(digit_matching_rows) > 0:
                    all_matching_rows.append(digit_matching_rows)
                    self.emit_status(f"使用纯数字匹配找到 {len(digit_matching_rows)} 行数据: '{operation_code}' (数字部分: {operation_digits})")
                    
                    # 输出匹配到的手机名称，帮助调试
                    phone_names = digit_matching_rows['手机名称'].astype(str).tolist()
                    self.emit_status(f"纯数字匹配找到的手机名称: {phone_names}")
                
                # 清理临时列
                if '运营编码_纯数字' in processed_daily_work_df.columns:
                    processed_daily_work_df.drop(columns=['运营编码_纯数字'], inplace=True)

        # 合并所有匹配的行
        if all_matching_rows:
            import pandas as pd
            combined_rows = pd.concat(all_matching_rows, ignore_index=True)
            
            # 输出所有匹配的行，帮助调试
            self.emit_status(f"运营编码 '{operation_code}' 总共匹配到 {len(combined_rows)} 行数据")
        else:
            combined_rows = pd.DataFrame()
            self.emit_status(f"⚠️ 运营编码 '{operation_code}' 在每日工作总表中未找到匹配数据")

        if len(combined_rows) > 0:
            # 获取处理后的手机名称
            phone_names = combined_rows['手机名称_处理后'].astype(str).str.strip().unique()
            phone_names = [name for name in phone_names if name and name != 'nan' and name != '']
            
            # 输出原始手机名称，帮助调试
            original_phone_names = combined_rows['手机名称'].astype(str).str.strip().unique()
            original_phone_names = [name for name in original_phone_names if name and name != 'nan' and name != '']
            self.emit_status(f"运营编码 '{operation_code}' 匹配到的原始手机名称: {original_phone_names}")
            self.emit_status(f"运营编码 '{operation_code}' 处理后的手机名称: {phone_names}")

            if not phone_names:
                self.emit_status(f"⚠️ 运营编码 '{operation_code}' 匹配到的手机名称为空")
                return ""  # 修改为返回空字符串而不是错误信息
            elif len(phone_names) == 1:
                result = phone_names[0]
                self.emit_status(f"✅ 运营编码 '{operation_code}' 最终使用手机名称: '{result}'")
                return result
            else:
                # 多个手机名称，只返回第一个，避免混合分配
                result = phone_names[0]
                self.emit_status(f"⚠️ 运营编码 '{operation_code}' 匹配到多个手机名称: {phone_names}，选择第一个: '{result}'")
                return result

        # 第三步：如果还是没找到，尝试从文件名或其他信息推断
        if operation_data and operation_code in operation_data:
            file_name = operation_data[operation_code].get('file_name', '')
            if file_name:
                # 尝试从文件名中提取手机信息
                inferred_phone = self.infer_phone_name_from_file_info(file_name, '', operation_code)
                if inferred_phone:
                    self.emit_status(f"从文件名推断手机名称: '{operation_code}' -> '{inferred_phone}'")
                    return inferred_phone

        # 如果没有找到
        self.emit_status(f"⚠️ 运营编码 '{operation_code}' 无法获取手机名称: 所有匹配方法均失败")
        return ""  # 修改为返回空字符串而不是错误信息
            
    def get_daily_work_codes_for_operation(self, operation_code, operation_data):
        """
        获取运营编码对应的每日工作总表编码

        Args:
            operation_code: 运营表格中的编码（可能是纯数字）
            operation_data: 运营数据字典

        Returns:
            set: 对应的每日工作总表编码集合
        """
        if not operation_data or operation_code not in operation_data:
            # 如果没有运营数据或编码不存在，直接返回原编码
            return {operation_code}

        op_info = operation_data[operation_code]

        # 检查是否有二次匹配信息
        if 'secondary_match' in op_info:
            match_info = op_info['secondary_match']
            if 'all_matches' in match_info:
                # 有多个匹配，返回所有匹配
                return set(match_info['all_matches'])
            else:
                # 只有一个匹配
                return {match_info['matched_code']}
        else:
            # 没有二次匹配，直接使用原编码
            return {operation_code}

    def infer_phone_groups_from_operation_data(self, operation_data):
        """从运营表格数据中推断手机分组"""
        phone_operation_mapping = {}

        self.emit_status("开始从运营表格推断手机分组...")

        for operation_code, op_info in operation_data.items():
            # 尝试从文件名中提取手机信息
            file_name = op_info.get('file_name', '')
            file_path = op_info.get('file_path', '')

            # 推断手机名称的策略
            inferred_phone = self.infer_phone_name_from_file_info(file_name, file_path, operation_code)

            if inferred_phone:
                if inferred_phone not in phone_operation_mapping:
                    phone_operation_mapping[inferred_phone] = set()
                phone_operation_mapping[inferred_phone].add(operation_code)
                self.emit_status(f"推断: 运营编码 {operation_code} -> 手机 {inferred_phone}")

        if phone_operation_mapping:
            self.emit_status(f"成功推断出 {len(phone_operation_mapping)} 个手机分组")
            for phone, codes in phone_operation_mapping.items():
                self.emit_status(f"  {phone}: {len(codes)} 个运营编码")
        else:
            self.emit_status("无法从运营表格推断出手机分组")

        return phone_operation_mapping

    def infer_phone_name_from_file_info(self, file_name, file_path, operation_code):
        """从文件信息中推断手机名称"""
        import re

        # 策略1: 从文件名中查找手机相关关键词
        phone_patterns = [
            r'手机(\d+)',           # 手机1, 手机2
            r'phone(\d+)',          # phone1, phone2
            r'机(\d+)',             # 机1, 机2
            r'(\d+)号机',           # 1号机, 2号机
            r'设备(\d+)',           # 设备1, 设备2
            r'device(\d+)',         # device1, device2
        ]

        for pattern in phone_patterns:
            match = re.search(pattern, file_name, re.IGNORECASE)
            if match:
                phone_num = match.group(1)
                return f"手机{phone_num}"

        # 策略2: 从运营编码中提取可能的手机标识
        # 有些运营编码可能包含手机信息，如 GL-123456789-A1, GL-123456789-B2
        code_phone_match = re.search(r'-([A-Z])(\d+)$', operation_code)
        if code_phone_match:
            letter = code_phone_match.group(1)
            number = code_phone_match.group(2)
            return f"手机{letter}{number}"

        # 策略3: 从运营编码后缀推断
        # 如 GL-123456789-A, GL-123456789-B 可能对应不同手机
        suffix_match = re.search(r'-([A-Z])$', operation_code)
        if suffix_match:
            suffix = suffix_match.group(1)
            # 将字母转换为数字 A=1, B=2, C=3...
            phone_num = ord(suffix) - ord('A') + 1
            return f"手机{phone_num}"

        # 策略4: 如果都无法推断，返回基于运营编码的分组
        # 这样至少能保证相同运营编码不会被拆分
        return None

    def allocate_by_operation_groups(self, operation_data, available_staff,
                                   allocated_data, staff_operation_codes, unmatched_data, daily_work_df=None, phone_to_staff=None):
        """按运营编码整体分配，保持每个运营编码的完整性"""
        self.emit_status("🔢 开始按运营编码整体分配...")

        # 初始化客服的数据量统计
        staff_data_counts = {staff: 0 for staff in available_staff}
        
        # 初始化手机到客服的映射（如果未提供）
        if phone_to_staff is None:
            phone_to_staff = {}

        # 计算每个运营编码的数据量并排序（从大到小）
        operation_data_counts = []
        for operation_code, op_info in operation_data.items():
            data_count = op_info.get('_data_count', len(op_info['data_rows']))
            if data_count > 0:
                operation_data_counts.append((operation_code, data_count, op_info))

        # 按数据量排序（从大到小）
        operation_data_counts.sort(key=lambda x: x[1], reverse=True)

        self.emit_status(f"🔢 开始整体分配 {len(operation_data_counts)} 个运营编码")

        # 预先检查所有运营编码的每日工作数据量是否足够
        if daily_work_df is not None and '运营编码' in daily_work_df.columns:
            self.emit_status("检查每日工作总表数据量是否足够...")
            daily_code_counts = {}
            
            for operation_code, data_count, op_info in operation_data_counts:
                # 查找每日工作总表中该运营编码的数据量
                daily_codes = self.get_daily_work_codes_for_operation(operation_code, operation_data)
                
                # 提取纯数字编码用于匹配
                digit_codes = set()
                for daily_code in daily_codes:
                    digit_code = self.extract_digits_from_code(daily_code)
                    if digit_code:
                        digit_codes.add(digit_code)
                
                # 同时使用原始编码和纯数字编码进行匹配
                daily_data = daily_work_df[
                    (daily_work_df['运营编码'].isin(daily_codes)) | 
                    (daily_work_df['运营编码'].astype(str).apply(self.extract_digits_from_code).isin(digit_codes))
                ]
                daily_count = len(daily_data)
                daily_code_counts[operation_code] = daily_count
                
                # 检查每日工作总表数据量是否足够
                if daily_count < data_count and daily_count > 0:
                    self.emit_status(f"⚠️ 运营编码 '{operation_code}' 每日工作数据量不足: {daily_count} < {data_count}")
                    self.emit_status(f"⚠️ 请确保每日工作总表中的编码数量充足，当前数据将被保留但可能导致分配不平衡")

        for operation_code, data_count, op_info in operation_data_counts:
            if available_staff:
                # 尝试从每日工作总表中获取手机名称
                phone_name = self.get_phone_name_from_daily_work(operation_code, daily_work_df, operation_data)
                
                # 检查这个手机是否已经分配给某个客服
                if phone_name and phone_name in phone_to_staff:
                    # 如果已分配，使用之前分配的客服
                    assigned_staff = phone_to_staff[phone_name]
                    self.emit_status(f"📱 手机 '{phone_name}' 已分配给客服 '{assigned_staff}'，保持一致性")
                else:
                    # 如果未分配或没有手机名称，选择当前数据量最少的客服
                    assigned_staff = min(staff_data_counts.keys(), key=lambda x: staff_data_counts[x])
                    # 如果有手机名称，记录手机分配给了哪个客服
                    if phone_name:
                        phone_to_staff[phone_name] = assigned_staff

                # 获取每日工作总表中该运营编码的数据量
                if daily_work_df is not None and '运营编码' in daily_work_df.columns:
                    # 查找每日工作总表中该运营编码的数据量
                    daily_codes = self.get_daily_work_codes_for_operation(operation_code, operation_data)
                    
                    # 提取纯数字编码用于匹配
                    digit_codes = set()
                    for daily_code in daily_codes:
                        digit_code = self.extract_digits_from_code(daily_code)
                        if digit_code:
                            digit_codes.add(digit_code)
                    
                    # 同时使用原始编码和纯数字编码进行匹配
                    daily_data = daily_work_df[
                        (daily_work_df['运营编码'].isin(daily_codes)) | 
                        (daily_work_df['运营编码'].astype(str).apply(self.extract_digits_from_code).isin(digit_codes))
                    ]
                    daily_count = len(daily_data)
                    
                    # 检查并处理超出数据量
                    if daily_count > 0:  # 只有在找到每日工作数据时才检查
                        self.check_and_handle_excess_data(operation_code, op_info, daily_count, unmatched_data)

                # 整个运营编码的所有数据都分配给同一个客服
                complete_table = pd.concat([
                    op_info['header_rows'],
                    op_info['data_rows']  # 所有数据行都分配给这个客服
                ], ignore_index=True)

                sheet_name = f"{operation_code}_{assigned_staff}"
                allocated_data[sheet_name] = {
                    'data': complete_table,
                    'operation_code': operation_code,
                    'staff_name': assigned_staff,
                    'row_count': data_count,
                    'file_path': op_info['file_path'],
                    'file_name': op_info['file_name'],
                    'original_df': op_info['original_df'],
                    'phone_name': phone_name if phone_name else ""  # 只有在手机名称不为空时才记录
                }

                staff_operation_codes[assigned_staff].add(operation_code)
                staff_data_counts[assigned_staff] += data_count

                phone_info = f"，手机: {phone_name}" if phone_name else ""
                self.emit_status(f"📄 运营编码 '{operation_code}' → 客服 '{assigned_staff}'，数据量: {data_count}{phone_info}")
            else:
                # 如果没有在班客服，将数据标记为未分配
                unmatched_data.extend(op_info['data_rows'].to_dict('records'))
                self.emit_status(f"⚠️ 运营编码 '{operation_code}' 无可用客服，已移至未匹配数据")

        # 输出最终的数据量分配统计
        if staff_data_counts:
            self.emit_status(f"📊 整体分配完成，客服数据量统计:")
            for staff, count in staff_data_counts.items():
                self.emit_status(f"  👤 {staff}: {count} 条数据")
