#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试手机名称筛选导致运营编码丢失的问题
"""

import sys
import pandas as pd
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_phone_filtering_issue():
    """演示手机名称筛选导致运营编码丢失的问题"""
    
    print("🧪 测试手机名称筛选导致运营编码丢失的问题...")
    print("=" * 60)
    
    # 模拟每日工作总表数据 - 创建会导致丢失的场景
    daily_work_data = {
        '运营编码': ['10030650788460', '10030650788461', '10030650788462', '945258864137'],
        '手机名称': ['小时', '玲玲-1', '木木', '小甜心'],  # 注意：10030650788460对应的是"小时"，不是"小甜心"
        '订单编号': ['ORDER001', 'ORDER002', 'ORDER003', 'ORDER004'],
        '评价内容': ['好评1', '好评2', '好评3', '好评4']
    }
    daily_work_df = pd.DataFrame(daily_work_data)

    print("📊 模拟每日工作总表数据:")
    print(daily_work_df)
    print()

    # 模拟客服分配情况
    # 客服陈彪分配了多个运营编码，但使用手机"小甜心"
    staff_name = "陈彪"
    daily_work_codes = {'10030650788460', '10030650788461', '10030650788462'}  # 分配的运营编码
    staff_phones = {'小甜心'}  # 客服使用的手机
    
    print(f"👤 客服 {staff_name} 的分配情况:")
    print(f"  分配的运营编码: {daily_work_codes}")
    print(f"  使用的手机: {staff_phones}")
    print()
    
    # 第一步：按运营编码筛选
    print("🔍 第一步：按运营编码筛选")
    staff_daily_data = daily_work_df[daily_work_df['运营编码'].isin(daily_work_codes)].copy()
    print(f"  按运营编码筛选结果: {len(staff_daily_data)} 条数据")
    print(staff_daily_data[['运营编码', '手机名称']])
    print()
    
    # 第二步：按手机名称进一步筛选
    print("🔍 第二步：按手机名称进一步筛选")
    matching_phone_data = staff_daily_data[staff_daily_data['手机名称'].isin(staff_phones)]
    print(f"  匹配手机名称的数据: {len(matching_phone_data)} 条")
    print(matching_phone_data[['运营编码', '手机名称']])
    print()
    
    # 检查丢失的运营编码
    print("🔍 检查丢失的运营编码")
    missing_codes = set()
    for code in daily_work_codes:
        if code in staff_daily_data['运营编码'].values and code not in matching_phone_data['运营编码'].values:
            missing_codes.add(code)
    
    if missing_codes:
        print(f"  ⚠️ 发现 {len(missing_codes)} 个运营编码在手机名称筛选后丢失: {missing_codes}")
        
        # 找出丢失的数据
        missing_data = staff_daily_data[staff_daily_data['运营编码'].isin(missing_codes)]
        print(f"  丢失的数据:")
        print(missing_data[['运营编码', '手机名称']])
        print()
        
        # 分析丢失原因
        print("📋 丢失原因分析:")
        for code in missing_codes:
            code_data = staff_daily_data[staff_daily_data['运营编码'] == code]
            phones_in_data = set(code_data['手机名称'].tolist())
            print(f"  运营编码 {code}:")
            print(f"    每日工作总表中的手机名称: {phones_in_data}")
            print(f"    客服分配的手机名称: {staff_phones}")
            print(f"    交集: {phones_in_data & staff_phones}")
            if not (phones_in_data & staff_phones):
                print(f"    ❌ 没有交集，导致数据丢失")
            print()
        
        # 演示修复策略
        print("🔧 修复策略:")
        print("  1. 保留匹配手机名称的数据")
        print("  2. 对于丢失的运营编码，保留手机名称为空或属于该客服的数据")
        
        # 筛选丢失编码中相关的数据
        filtered_missing_data = missing_data[
            (missing_data['手机名称'].isin(staff_phones)) |
            (missing_data['手机名称'].isna()) |
            (missing_data['手机名称'] == '') |
            (missing_data['手机名称'].str.strip() == '')
        ]
        
        print(f"  从丢失编码中筛选出 {len(filtered_missing_data)} 条相关数据")
        
        # 合并数据
        final_data = pd.concat([matching_phone_data, filtered_missing_data]).drop_duplicates()
        print(f"  最终数据量: {len(final_data)} 条")
        print("  最终数据:")
        print(final_data[['运营编码', '手机名称']])
        
    else:
        print("  ✅ 没有运营编码丢失")

if __name__ == "__main__":
    test_phone_filtering_issue()
