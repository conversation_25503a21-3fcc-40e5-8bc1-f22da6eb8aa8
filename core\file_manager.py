#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件管理模块
负责读取和管理各种Excel文件
"""

import pandas as pd
from pathlib import Path
from PyQt6.QtCore import pyqtSignal
from utils.helpers import performance_monitor


class FileManager:
    """文件管理器"""
    
    def __init__(self, status_callback=None):
        self.status_callback = status_callback
    
    def emit_status(self, message):
        """发送状态消息"""
        if self.status_callback:
            self.status_callback(message)
    
    def read_assignment_file(self, file_path):
        """读取客服排班表"""
        try:
            assignment_df = pd.read_excel(file_path)
            self.emit_status("已读取客服排班表")
            return assignment_df
        except Exception as e:
            raise Exception(f"读取客服排班表失败: {str(e)}")
    
    def read_daily_work_file(self, file_path):
        """读取每日工作总表"""
        try:
            daily_work_df = pd.read_excel(file_path)
            self.emit_status("已读取每日工作总表")
            return daily_work_df
        except Exception as e:
            raise Exception(f"读取每日工作总表失败: {str(e)}")
    
    @performance_monitor
    def read_operation_files(self, file_paths):
        """读取运营表格文件"""
        operation_data = {}
        
        self.emit_status(f"📂 读取 {len(file_paths)} 个运营表格文件")
        
        for i, op_file in enumerate(file_paths):
            try:
                # 性能优化：先只读取前3行来获取运营编码
                header_df = pd.read_excel(op_file, header=None, nrows=3)

                if len(header_df) < 3:
                    self.emit_status(f"⚠️ {Path(op_file).name} 行数不足，跳过")
                    continue

                # 从B1获取运营编码
                raw_operation_code = str(header_df.iloc[0, 1]).strip() if len(header_df.columns) >= 2 else ""

                # 清理运营编码，过滤掉商品ID
                operation_code = self.clean_operation_code(raw_operation_code)

                if operation_code and operation_code != 'nan':
                    # 只有在运营编码有效时才读取完整文件
                    op_df = pd.read_excel(op_file, header=None)

                    # 使用视图而不是复制来提高性能
                    header_rows = op_df.iloc[:2]  # 前两行作为表头（视图）
                    data_rows = op_df.iloc[2:]    # 第三行以下的数据（视图）
                    
                    # 过滤空白行
                    # 判断标准：如果一行中所有单元格都是空或NaN或只包含空格，则认为是空白行
                    non_empty_rows = data_rows.apply(lambda row: not all(
                        pd.isna(val) or (isinstance(val, str) and val.strip() == '') 
                        for val in row
                    ), axis=1)
                    
                    # 只保留非空行
                    filtered_data_rows = data_rows[non_empty_rows]
                    
                    # 如果过滤掉了空白行，记录日志
                    empty_rows_count = len(data_rows) - len(filtered_data_rows)
                    if empty_rows_count > 0:
                        self.emit_status(f"🧹 {Path(op_file).name}: 过滤掉 {empty_rows_count} 行空白数据")

                    # 保存运营表格的完整信息
                    operation_data[operation_code] = {
                        'file_name': Path(op_file).stem,
                        'file_path': op_file,
                        'header_rows': header_rows,
                        'data_rows': filtered_data_rows,  # 使用过滤后的数据行
                        'original_df': op_df,  # 保存原始数据引用
                        '_data_count': len(filtered_data_rows)  # 缓存过滤后的数据行数
                    }

                    self.emit_status(f"📄 {Path(op_file).name}: 编码 {operation_code}, 数据 {len(filtered_data_rows)} 行")
                else:
                    self.emit_status(f"⚠️ {Path(op_file).name} B1单元格未找到有效编码")

            except Exception as e:
                self.emit_status(f"❌ 读取 {Path(op_file).name} 失败: {str(e)}")
                continue
        
        self.emit_status(f"✅ 读取完成，共 {len(operation_data)} 个有效运营编码")
        return operation_data
    
    def find_unmatched_operations(self, daily_work_df, operation_data):
        """查找在每日工作总表中没有匹配数据的运营编码 - 支持二次匹配功能"""
        if daily_work_df is None or len(daily_work_df) == 0:
            return list(operation_data.keys())

        if '运营编码' not in daily_work_df.columns:
            return list(operation_data.keys())

        # 性能优化：使用集合操作而不是循环
        daily_codes = set(daily_work_df['运营编码'].astype(str).str.strip())
        operation_codes = set(operation_data.keys())
        
        # 提取每日工作总表中的纯数字编码
        daily_digit_codes = set()
        for code in daily_codes:
            digit_code = self.extract_digits_from_code(code)
            if digit_code:
                daily_digit_codes.add(digit_code)
        
        # 第一次匹配：直接匹配或纯数字匹配
        matched_codes = set()
        unmatched_codes = []
        
        for op_code in operation_codes:
            # 直接匹配
            if op_code in daily_codes:
                matched_codes.add(op_code)
                continue
                
            # 纯数字匹配
            digit_code = self.extract_digits_from_code(op_code)
            if digit_code and digit_code in daily_digit_codes:
                matched_codes.add(op_code)
                # 记录纯数字匹配信息
                if op_code in operation_data:
                    # 找到匹配的原始编码
                    matched_daily_codes = []
                    for daily_code in daily_codes:
                        if self.extract_digits_from_code(daily_code) == digit_code:
                            matched_daily_codes.append(daily_code)
                    
                    if matched_daily_codes:
                        operation_data[op_code]['secondary_match'] = {
                            'original_code': op_code,
                            'matched_code': matched_daily_codes[0],
                            'all_matches': matched_daily_codes,
                            'match_type': '纯数字匹配（自动）'
                        }
                        self.emit_status(f"纯数字自动匹配成功: '{op_code}' -> {matched_daily_codes}")
                continue
                
            # 如果都没匹配上，添加到未匹配列表
            unmatched_codes.append(op_code)

        # 如果有未匹配的编码，进行二次匹配
        if unmatched_codes:
            self.emit_status(f"第一次匹配完成，{len(matched_codes)} 个编码匹配成功，{len(unmatched_codes)} 个编码需要二次匹配")

            # 进行二次匹配：去掉字母，保留纯数字进行匹配
            secondary_matched = self.perform_secondary_matching(unmatched_codes, daily_codes, operation_data)

            # 更新未匹配列表
            for original_code, matched_daily_code in secondary_matched.items():
                if original_code in unmatched_codes:
                    unmatched_codes.remove(original_code)
                    self.emit_status(f"二次匹配成功: '{original_code}' -> '{matched_daily_code}'")

        return unmatched_codes

    def perform_secondary_matching(self, unmatched_codes, daily_codes, operation_data):
        """
        执行二次匹配：当运营表格B1无法匹配到每日工作总表的运营编码时，
        将每日工作总表的字母去掉，保留纯数字再次匹配

        注意：保持运营表格中的原始编码格式，不进行格式转换

        Args:
            unmatched_codes: 第一次匹配失败的运营编码列表
            daily_codes: 每日工作总表中的运营编码集合
            operation_data: 运营表格数据

        Returns:
            dict: {原始运营编码: 匹配到的每日工作编码} 的映射
        """
        import re
        secondary_matches = {}

        self.emit_status("开始执行二次匹配（纯数字匹配）...")

        # 为每个未匹配的运营编码提取纯数字
        for operation_code in unmatched_codes:
            # 提取运营编码中的纯数字部分
            operation_digits = self.extract_digits_from_code(operation_code)

            if not operation_digits or len(operation_digits) < 6:  # 数字太少，不适合匹配
                continue

            self.emit_status(f"尝试二次匹配 '{operation_code}' (数字部分: {operation_digits})")

            # 在每日工作总表的编码中查找所有匹配的编码
            all_matches = self.find_all_digit_matches(operation_digits, daily_codes)

            if all_matches:
                if len(all_matches) == 1:
                    # 只有一个匹配
                    matched_code = all_matches[0]
                    secondary_matches[operation_code] = matched_code
                    self.emit_status(f"  ✅ 找到唯一匹配: '{operation_code}' -> '{matched_code}'")

                    # 保持原始运营编码格式，不更新键值
                    # 只在原始数据中添加二次匹配标记，用于后续查找手机名称等信息
                    if operation_code in operation_data:
                        operation_data[operation_code]['secondary_match'] = {
                            'original_code': operation_code,
                            'matched_code': matched_code,
                            'match_type': '纯数字匹配'
                        }
                        self.emit_status(f"  保持原始编码格式: '{operation_code}'，匹配信息已记录")
                else:
                    # 多个匹配，需要特殊处理
                    self.emit_status(f"  ⚠️ 找到多个匹配: '{operation_code}' -> {all_matches}")

                    # 使用第一个匹配作为主要匹配，但记录所有匹配
                    primary_match = all_matches[0]
                    secondary_matches[operation_code] = primary_match

                    if operation_code in operation_data:
                        operation_data[operation_code]['secondary_match'] = {
                            'original_code': operation_code,
                            'matched_code': primary_match,
                            'all_matches': all_matches,
                            'match_type': '纯数字匹配（多个匹配）'
                        }
                        self.emit_status(f"  使用主要匹配: '{primary_match}'，所有匹配已记录")
            else:
                self.emit_status(f"  ❌ 未找到匹配: '{operation_code}' (数字部分: {operation_digits})")

        if secondary_matches:
            self.emit_status(f"二次匹配完成，成功匹配 {len(secondary_matches)} 个运营编码")
        else:
            self.emit_status("二次匹配完成，未找到任何匹配")

        return secondary_matches

    def extract_digits_from_code(self, code):
        """
        从运营编码中提取纯数字部分

        对于带后缀的编码（如 ZDDLLS-3740581615068381598-B1），
        只提取主体部分的数字（3740581615068381598），
        不包括后缀中的数字（B1中的1）

        对于纯数字编码（如 10030650788460），直接返回该数字
        """
        import re
        if not code:
            return ""

        code = str(code).strip()

        # 首先检查是否为纯数字编码
        if re.match(r'^\d+$', code):
            return code

        # 尝试匹配标准运营编码格式：PREFIX-MAINCODE-SUFFIX
        # 其中 SUFFIX 可能是 A1, B1, C2 等字母+数字组合
        match = re.match(r'^([A-Z]+)-([A-Z0-9]+)-([A-Z][0-9]*)$', code)
        if match:
            # 如果匹配到标准格式，只从主体部分提取数字
            main_code = match.group(2)
            digits = re.findall(r'\d', main_code)
            return ''.join(digits)

        # 尝试匹配无后缀格式：PREFIX-MAINCODE
        match = re.match(r'^([A-Z]+)-([A-Z0-9]+)$', code)
        if match:
            # 如果匹配到无后缀格式，从主体部分提取数字
            main_code = match.group(2)
            digits = re.findall(r'\d', main_code)
            return ''.join(digits)

        # 如果不匹配标准格式，回退到原来的逻辑（提取所有数字）
        digits = re.findall(r'\d', code)
        return ''.join(digits)

    def find_all_digit_matches(self, target_digits, daily_codes):
        """
        在每日工作总表编码中查找所有数字匹配的编码

        Args:
            target_digits: 目标数字字符串
            daily_codes: 每日工作总表编码集合

        Returns:
            list: 所有匹配的编码列表，按匹配分数排序
        """
        if not target_digits or len(target_digits) < 6:
            return []

        matches = []

        for daily_code in daily_codes:
            daily_digits = self.extract_digits_from_code(daily_code)

            if not daily_digits:
                continue

            # 计算匹配分数
            score = self.calculate_digit_match_score(target_digits, daily_digits)

            # 只考虑完全匹配的编码
            if score == 1.0:
                matches.append((daily_code, score))

        # 按分数排序（虽然都是1.0，但保持一致性）
        matches.sort(key=lambda x: x[1], reverse=True)

        # 返回编码列表
        return [match[0] for match in matches]

    def find_best_digit_match(self, target_digits, daily_codes):
        """
        在每日工作总表编码中查找最佳的数字匹配（保持向后兼容）

        Args:
            target_digits: 目标数字字符串
            daily_codes: 每日工作总表编码集合

        Returns:
            str: 最佳匹配的编码，如果没有找到则返回None
        """
        matches = self.find_all_digit_matches(target_digits, daily_codes)
        return matches[0] if matches else None

    def calculate_digit_match_score(self, digits1, digits2):
        """
        计算两个数字字符串的匹配分数

        Args:
            digits1: 第一个数字字符串
            digits2: 第二个数字字符串

        Returns:
            float: 匹配分数 (0.0 - 1.0)
        """
        if not digits1 or not digits2:
            return 0.0

        # 如果完全相同，返回1.0
        if digits1 == digits2:
            return 1.0

        # 检查较短的数字串是否完全包含在较长的数字串中
        shorter, longer = (digits1, digits2) if len(digits1) <= len(digits2) else (digits2, digits1)

        if shorter in longer:
            # 计算包含匹配的分数：匹配长度 / 较长字符串长度
            return len(shorter) / len(longer)

        # 计算最长公共子序列的长度
        lcs_length = self.longest_common_subsequence_length(digits1, digits2)
        max_length = max(len(digits1), len(digits2))

        return lcs_length / max_length if max_length > 0 else 0.0

    def longest_common_subsequence_length(self, str1, str2):
        """计算最长公共子序列的长度"""
        m, n = len(str1), len(str2)

        # 创建DP表
        dp = [[0] * (n + 1) for _ in range(m + 1)]

        # 填充DP表
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if str1[i-1] == str2[j-1]:
                    dp[i][j] = dp[i-1][j-1] + 1
                else:
                    dp[i][j] = max(dp[i-1][j], dp[i][j-1])

        return dp[m][n]

    def move_unmatched_to_separate_data(self, operation_data, unmatched_codes):
        """将未匹配的运营编码移动到单独的数据结构中"""
        unmatched_data = []
        
        for code in unmatched_codes:
            if code in operation_data:
                # 将数据行转换为字典格式
                data_rows = operation_data[code]['data_rows']
                unmatched_data.extend(data_rows.to_dict('records'))
                
                # 从operation_data中删除
                del operation_data[code]
                self.emit_status(f"运营编码 {code} 已移至未匹配数据")
        
        return unmatched_data

    def move_unmatched_to_separate_data_with_analysis(self, operation_data, unmatched_codes, daily_work_df):
        """将未匹配的运营编码数据移至单独的数据结构，并添加分析信息"""
        unmatched_data = []
        
        for operation_code in unmatched_codes:
            if operation_code in operation_data:
                op_info = operation_data[operation_code]
                
                # 分析未匹配原因
                unmatch_reason, detailed_analysis, solution = self.analyze_unmatched_reason(
                    operation_code, daily_work_df, operation_data
                )
                
                # 将数据行转换为字典格式并添加分析信息
                for _, row in op_info['data_rows'].iterrows():
                    unmatched_item = row.to_dict()
                    
                    # 添加分析信息
                    unmatched_item.update({
                        '运营编码': operation_code,
                        '未匹配原因': unmatch_reason,
                        '详细分析': detailed_analysis,
                        '建议解决方案': solution,
                        '原始文件': op_info.get('file_path', '未知'),
                        '文件名': op_info.get('file_name', '未知'),  # 添加文件名，用于提取店铺名称
                        '数据行号': _ + 3,  # 假设数据从第3行开始
                        '分析时间': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')
                    })
                    
                    unmatched_data.append(unmatched_item)
                
                # 从原始数据中移除未匹配的运营编码
                operation_data.pop(operation_code)
        
        return unmatched_data

    def analyze_unmatched_reason(self, operation_code, daily_work_df, operation_data=None):
        """分析运营编码未匹配的具体原因，提供详细分析和解决方案"""
        reason = "未知原因"
        details = "无法确定未匹配的具体原因"
        solution = "请检查运营编码格式和每日工作总表是否完整"
        
        # 获取每日工作总表中的所有运营编码用于对比
        daily_codes = set()
        if daily_work_df is not None and '运营编码' in daily_work_df.columns:
            daily_codes = set(daily_work_df['运营编码'].astype(str).str.strip())
        
        # 检查运营编码是否为空或无效
        if not operation_code or operation_code == 'nan':
            reason = "运营编码为空"
            details = "运营表格中的B1单元格没有有效的运营编码"
            solution = "请在运营表格B1单元格填写正确的运营编码"
            return reason, details, solution
        
        # 检查运营编码格式是否有效
        if not self.is_valid_operation_code_format(operation_code):
            reason = "运营编码格式无效"
            details = f"运营编码 '{operation_code}' 不符合标准格式要求"
            solution = "请使用标准格式的运营编码，如 'GL-1234567890-A' 或纯数字格式（至少8位数字）"
            return reason, details, solution
        
        # 检查是否在每日工作总表中找到完全匹配
        if operation_code in daily_codes:
            reason = "运营编码存在但未能正确匹配"
            details = "运营编码在每日工作总表中存在，但由于其他原因未能匹配"
            solution = "请检查运营编码对应的手机分组或数据格式"
            return reason, details, solution
        
        # 尝试查找相似的运营编码
        similar_codes = []
        for daily_code in daily_codes:
            # 检查前缀是否相同
            if operation_code and daily_code and len(operation_code) > 3 and len(daily_code) > 3:
                if operation_code[:3] == daily_code[:3]:
                    similar_codes.append(daily_code)
        
        # 检查是否有相似的运营编码
        if similar_codes:
            reason = "运营编码与每日工作总表中的编码不完全匹配"
            details = f"运营编码 '{operation_code}' 在每日工作总表中未找到，但存在相似编码: {', '.join(similar_codes[:5])}"
            solution = "请检查运营编码是否有拼写错误或格式差异"
            return reason, details, solution
        
        # 尝试提取纯数字部分进行匹配
        op_digits = self.extract_digits_from_code(operation_code)
        if op_digits and len(op_digits) >= 6:  # 至少需要6位数字才有意义
            digit_matched = False
            for daily_code in daily_codes:
                daily_digits = self.extract_digits_from_code(daily_code)
                if daily_digits and op_digits == daily_digits:
                    digit_matched = True
                    similar_codes.append(daily_code)
            
            if digit_matched:
                reason = "纯数字部分匹配但完整编码不匹配"
                details = f"运营编码 '{operation_code}' 的纯数字部分与每日工作总表中的某些编码匹配，但完整编码不同: {', '.join(similar_codes[:5])}"
                solution = "请考虑使用二次匹配功能，或统一运营编码格式"
                return reason, details, solution
        
        # 如果都不匹配，则可能是每日工作总表中确实没有对应的编码
        reason = "每日工作总表中没有对应的运营编码"
        details = f"运营编码 '{operation_code}' 在每日工作总表中完全没有找到，也没有相似的编码"
        solution = "请检查每日工作总表是否完整，或确认运营编码是否正确"
        
        return reason, details, solution

    def is_valid_operation_code_format(self, code):
        """
        检查运营编码格式是否有效

        通用运营编码格式识别：
        - 标准格式：以字母开头，包含连字符分隔
        - 纯数字格式：纯数字编码（至少8位数字）
        - 主体部分包含数字（通常较长）
        - 可选的字母后缀（支持字母+数字组合后缀，如A1、A2等）

        支持格式示例：
        - GL-123456789-A
        - ZDDGL-123456789-F
        - ABC-987654321-B
        - XYZ-111222333444555-C
        - DLS-10029969879149-A1
        - DLS-10026890480657-A2
        - JSB-10026890480657-A2
        - 10030650788460 (纯数字格式)
        """
        if not code:
            return False

        import re
        code = code.strip()

        # 首先检查是否为纯数字格式（至少8位数字）
        if re.match(r'^\d{8,}$', code):
            return True

        # 通用运营编码格式：
        # 1. 以字母开头（1-15个字母，支持更长的前缀）
        # 2. 连字符分隔
        # 3. 主体部分：数字为主，可包含字母（至少6位）
        # 4. 可选的连字符+字母后缀（支持字母+数字组合，如A1、A2等）
        # 5. 可选的日期后缀：(YYYYMMDD)
        patterns = [
            # 带后缀和日期格式：PREFIX-MAINCODE-SUFFIX(YYYYMMDD)
            r'^[A-Z]{1,15}-[A-Z0-9]{6,}-[A-Z][0-9]{0,1}\(\d{8}\)$',
            # 带后缀格式：PREFIX-MAINCODE-SUFFIX
            r'^[A-Z]{1,15}-[A-Z0-9]{6,}-[A-Z][0-9]{0,1}$',
            # 无后缀带日期格式：PREFIX-MAINCODE(YYYYMMDD)
            r'^[A-Z]{1,15}-[A-Z0-9]{6,}\(\d{8}\)$',
            # 无后缀格式：PREFIX-MAINCODE
            r'^[A-Z]{1,15}-[A-Z0-9]{6,}$',
        ]

        for pattern in patterns:
            if re.match(pattern, code):
                # 额外验证：主体部分应该包含足够的数字
                parts = code.split('-')
                if len(parts) >= 2:
                    main_part = parts[1]  # 主体编码部分
                    # 主体部分应该至少包含6个数字
                    digit_count = sum(1 for c in main_part if c.isdigit())
                    if digit_count >= 6:
                        return True

        return False

    def find_similar_codes(self, target_code, available_codes, max_results=5):
        """查找相似的运营编码"""
        if not target_code or not available_codes:
            return []

        similar_codes = []
        target_lower = target_code.lower()

        for code in available_codes:
            code_lower = code.lower()

            # 检查是否包含相同的子字符串
            if len(target_code) > 3 and target_code[3:] in code:
                similar_codes.append(code)
            elif len(code) > 3 and code[3:] in target_code:
                similar_codes.append(code)
            # 检查编辑距离
            elif self.calculate_edit_distance(target_lower, code_lower) <= 2:
                similar_codes.append(code)

        return similar_codes[:max_results]

    def calculate_edit_distance(self, s1, s2):
        """计算两个字符串的编辑距离"""
        if len(s1) > len(s2):
            s1, s2 = s2, s1

        distances = range(len(s1) + 1)
        for i2, c2 in enumerate(s2):
            distances_ = [i2 + 1]
            for i1, c1 in enumerate(s1):
                if c1 == c2:
                    distances_.append(distances[i1])
                else:
                    distances_.append(1 + min((distances[i1], distances[i1 + 1], distances_[-1])))
            distances = distances_
        return distances[-1]

    def clean_operation_code(self, raw_code):
        """
        清理运营编码，自动过滤掉商品ID

        处理以下格式（支持任何符合格式的运营编码前缀）：
        1. PREFIX-1234567890123456789-A:商品ID123456 -> PREFIX-1234567890123456789-A
        2. PREFIX-1234567890123456789:商品ID123456 -> PREFIX-1234567890123456789
        3. PREFIX-1234567890123456789-A 商品ID123456 -> PREFIX-1234567890123456789-A
        4. PREFIX-1234567890123456789 商品ID123456 -> PREFIX-1234567890123456789
        5. 商品ID123456:PREFIX-1234567890123456789-A -> PREFIX-1234567890123456789-A

        支持的前缀包括但不限于：GL、ZDDGL、ABC、XYZ等任何字母组合
        """
        if not raw_code or raw_code == 'nan':
            return ""

        import re

        # 记录原始输入用于调试
        original_code = raw_code

        # 第一步：移除明显的商品ID和商品编码标识
        # 匹配 "商品ID" + 数字的模式（支持中英文冒号）
        raw_code = re.sub(r'商品ID\d*[:\s：]*', '', raw_code)
        # 匹配 "商品编码" 和 "商品编码：" 的模式（支持中英文冒号）
        raw_code = re.sub(r'商品编码[:\s：]*', '', raw_code)

        # 第二步：处理冒号分隔的情况（支持中英文冒号）
        colon_chars = [':', '：']  # 英文冒号和中文冒号
        for colon in colon_chars:
            if colon in raw_code:
                parts = raw_code.split(colon)
                # 找到符合运营编码格式的部分
                for part in parts:
                    part = part.strip()
                    if self.is_valid_operation_code_format(part):
                        raw_code = part
                        break
                # 如果没有找到完全有效的部分，尝试找到可能的运营编码（包含连字符和数字）
                if colon in raw_code:  # 说明上面的循环没有找到完全匹配的
                    for part in parts:
                        part = part.strip()
                        # 简单启发式：包含连字符且有足够数字的部分
                        if '-' in part and sum(1 for c in part if c.isdigit()) >= 6:
                            raw_code = part
                            break
                # 如果找到了运营编码部分，跳出外层循环
                if colon not in raw_code:
                    break

        # 第三步：处理空格分隔的情况
        if ' ' in raw_code:
            parts = raw_code.split()
            # 找到符合运营编码格式的部分
            for part in parts:
                part = part.strip()
                if self.is_valid_operation_code_format(part):
                    raw_code = part
                    break
            # 如果没有找到完全有效的部分，尝试找到可能的运营编码
            if ' ' in raw_code:  # 说明上面没有找到
                for part in parts:
                    part = part.strip()
                    # 简单启发式：包含连字符且有足够数字的部分
                    if '-' in part and sum(1 for c in part if c.isdigit()) >= 6:
                        raw_code = part
                        break

        # 第四步：处理日期后缀和其他后缀，将其移除（保留核心运营编码）
        # 处理格式如：DLS-4044309998742007808-C(20250606)-WP -> DLS-4044309998742007808-C

        # 1. 匹配并移除日期后缀 (YYYYMMDD) 及其后的所有内容
        date_with_suffix_pattern = r'\(\d{8}\).*$'
        if re.search(date_with_suffix_pattern, raw_code):
            original_with_date = raw_code
            raw_code = re.sub(date_with_suffix_pattern, '', raw_code)
            self.emit_status(f"移除日期后缀: '{original_with_date}' -> '{raw_code}'")

        # 2. 处理其他可能的后缀模式（如 -WP, -SUFFIX 等）
        # 但要保留标准的运营编码后缀（如 -A, -B, -C 等单字母后缀或 -A1, -A2 等字母+数字组合后缀）
        # 匹配模式：运营编码-字母数字后缀-其他后缀 -> 运营编码-字母数字后缀
        extra_suffix_pattern = r'(-[A-Z][0-9]{0,1})-[A-Z]{2,}.*$'
        if re.search(extra_suffix_pattern, raw_code):
            original_with_extra = raw_code
            raw_code = re.sub(extra_suffix_pattern, r'\1', raw_code)
            self.emit_status(f"移除额外后缀: '{original_with_extra}' -> '{raw_code}'")

        # 第五步：移除其他可能的商品ID模式
        # 移除纯数字ID（通常在末尾）
        raw_code = re.sub(r'[:\s]+\d+$', '', raw_code)

        # 第六步：移除其他常见的商品标识（支持中英文冒号）
        # 注意：要避免误删运营编码的合法后缀（如-A, -B等）
        patterns_to_remove = [
            r'^商品ID[:\s：]*',         # 开头的"商品ID："
            r'^商品编码[:\s：]*',       # 开头的"商品编码："
            r'[:\s：]+商品编码[:\s：]*', # 中间或末尾的"商品编码："
            r'[:\s：]+商品\d*$',        # 末尾的商品123（必须有分隔符）
            r'[:\s：]+产品\d*$',        # 末尾的产品123（必须有分隔符）
            r'[:\s：]+ID\d+$',          # 末尾的ID123（必须有数字）
            r'[:\s：]+编号\d+$',        # 末尾的编号123（必须有数字）
            r'[:\s：]+\d{6,}$',         # 末尾的长数字串（6位以上）
        ]

        for pattern in patterns_to_remove:
            raw_code = re.sub(pattern, '', raw_code, flags=re.IGNORECASE)

        # 第六步：清理空格和特殊字符
        cleaned_code = raw_code.strip()

        # 第七步：最后的清理 - 移除末尾的冒号和其他特殊字符
        cleaned_code = re.sub(r'[:：\s]+$', '', cleaned_code)  # 移除末尾的冒号和空格
        cleaned_code = cleaned_code.strip()

        # 第八步：验证清理后的运营编码格式
        if cleaned_code and self.is_valid_operation_code_format(cleaned_code):
            # 如果清理前后不同，记录日志
            if cleaned_code != original_code:
                self.emit_status(f"运营编码清理: '{original_code}' -> '{cleaned_code}'")
            return cleaned_code

        # 如果清理后格式不正确，尝试从原始字符串中提取标准格式的运营编码
        # 使用通用正则表达式匹配任何符合格式的运营编码
        # 匹配模式：字母前缀-数字主体-可选字母后缀-可选日期后缀-可选额外后缀
        operation_code_patterns = [
            # 纯数字格式（至少8位数字）
            r'(\d{8,})',
            # 复杂格式：DLS-4044309998742007808-C(20250606)-WP
            r'([A-Z]{1,15}-[A-Z0-9]{6,}-[A-Z][0-9]{0,1})\(\d{8}\)-[A-Z]{2,}',
            # 带后缀和日期格式：PREFIX-MAINCODE-SUFFIX(YYYYMMDD)
            r'([A-Z]{1,15}-[A-Z0-9]{6,}-[A-Z][0-9]{0,1})\(\d{8}\)',
            # 带后缀格式：PREFIX-MAINCODE-SUFFIX (包括字母+数字组合后缀，如A1、A2等)
            r'([A-Z]{1,15}-[A-Z0-9]{6,}-[A-Z][0-9]{0,1})',
            # 无后缀带日期格式：PREFIX-MAINCODE(YYYYMMDD)
            r'([A-Z]{1,15}-[A-Z0-9]{6,})\(\d{8}\)',
            # 无后缀格式：PREFIX-MAINCODE
            r'([A-Z]{1,15}-[A-Z0-9]{6,})',
        ]

        for pattern in operation_code_patterns:
            matches = re.findall(pattern, original_code)
            for match in matches:
                # 如果匹配结果是元组（包含捕获组），取第一个捕获组
                if isinstance(match, tuple):
                    clean_match = match[0]
                else:
                    clean_match = match

                # 再次确保移除任何残留的日期后缀
                clean_match = re.sub(r'\(\d{8}\)$', '', clean_match)

                if self.is_valid_operation_code_format(clean_match):
                    if clean_match != original_code:
                        self.emit_status(f"运营编码提取: '{original_code}' -> '{clean_match}'")
                    return clean_match

        # 如果都失败了，返回原始清理结果并记录警告
        if cleaned_code != original_code:
            self.emit_status(f"⚠️ 运营编码清理可能不完整: '{original_code}' -> '{cleaned_code}'")

        return cleaned_code
