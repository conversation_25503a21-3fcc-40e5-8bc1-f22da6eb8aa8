#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel生成器模块
负责生成最终的Excel文件
"""

import os
import pandas as pd
from pathlib import Path
from datetime import datetime, timedelta
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, Border, Side, Alignment
from openpyxl.worksheet.hyperlink import Hyperlink
from utils.helpers import generate_short_sheet_name, extract_shop_name, sort_by_pinyin


class ExcelGenerator:
    """Excel文件生成器"""
    
    def __init__(self, status_callback=None):
        self.status_callback = status_callback
    
    def emit_status(self, message):
        """发送状态消息"""
        if self.status_callback:
            self.status_callback(message)
    
    def generate_excel_files(self, allocated_data, output_path, unmatched_data=None):
        """生成Excel文件"""
        output_path = Path(output_path)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # 按客服分组数据，并合并相同原始运营编码的数据
        staff_data = {}
        
        for _, info in allocated_data.items():
            staff_name = info['staff_name']
            operation_code = info['operation_code']
            
            # 提取原始运营编码（去除手机标记）
            if '_手机_' in operation_code:
                base_operation_code = operation_code.split('_手机_')[0]
            else:
                base_operation_code = operation_code

            if staff_name not in staff_data:
                staff_data[staff_name] = {}

            # 使用原始运营编码作为键，合并相同运营编码的数据
            if base_operation_code not in staff_data[staff_name]:
                # 先生成基础的Sheet名称，稍后添加数量
                base_short_name = generate_short_sheet_name(base_operation_code)

                staff_data[staff_name][base_operation_code] = {
                    'base_sheet_name': base_short_name,  # 保存基础名称
                    'sheet_name': '',  # 最终名称稍后生成
                    'data_parts': [],  # 存储所有相关的数据部分
                    'total_rows': 0,
                    'operation_codes': set(),  # 记录所有相关的运营编码（包括手机标记版本）
                    'phone_names': set()  # 记录所有相关的手机名称
                }
            
            # 添加数据部分
            staff_data[staff_name][base_operation_code]['data_parts'].append(info)
            staff_data[staff_name][base_operation_code]['total_rows'] += info['row_count']
            staff_data[staff_name][base_operation_code]['operation_codes'].add(operation_code)
            
            # 记录手机名称（只记录非空的手机名称）
            if 'phone_name' in info and info['phone_name'] and info['phone_name'].strip():
                staff_data[staff_name][base_operation_code]['phone_names'].add(info['phone_name'].strip())

        # 为每个客服创建独立的Excel文件，保持原始样式
        if staff_data:
            # 获取明天的日期，格式为_MM.dd
            tomorrow = datetime.now() + timedelta(days=1)
            current_date = tomorrow.strftime("_%m.%d")

            for staff_name, staff_operations in staff_data.items():
                staff_file = output_path / f"{staff_name}_评价汇总{current_date}.xlsx"

                # 创建新的工作簿
                wb = Workbook()
                # 删除默认的工作表
                wb.remove(wb.active)

                sheet_created = False
                total_rows = 0
                summary_data = []  # 用于汇总Sheet的数据

                # 先按店铺名称排序staff_operations，确保工作表顺序与分配汇总表一致
                def get_shop_name_for_sorting(item):
                    """获取用于排序的店铺名称"""
                    base_operation_code, merged_info = item
                    if merged_info['data_parts']:
                        first_part = merged_info['data_parts'][0]
                        file_name = first_part.get('file_name', '')
                        shop_name = extract_shop_name(file_name)
                        return shop_name if shop_name else '未知店铺'
                    return '未知店铺'

                # 按店铺名称的拼音进行A-Z排序
                sorted_staff_operations = sort_by_pinyin(list(staff_operations.items()), key_func=get_shop_name_for_sorting)

                for base_operation_code, merged_info in sorted_staff_operations:
                    # 合并所有相关数据部分
                    combined_data_parts = []
                    combined_header = None
                    
                    for part_info in merged_info['data_parts']:
                        data_df = part_info['data']
                        if not data_df.empty:
                            if combined_header is None:
                                # 使用第一个部分的表头
                                combined_header = data_df.iloc[:2].copy()  # 前两行作为表头
                                combined_data_parts.append(data_df.iloc[2:])  # 数据行
                            else:
                                # 后续部分只取数据行
                                combined_data_parts.append(data_df.iloc[2:])
                    
                    if combined_data_parts and combined_header is not None:
                        # 合并所有数据
                        combined_data = pd.concat([combined_header] + combined_data_parts, ignore_index=True)

                        # 生成包含店铺名称和数量的最终Sheet名称
                        base_name = merged_info['base_sheet_name']
                        data_count = merged_info['total_rows']

                        # 从第一个数据部分获取文件名，提取店铺名称
                        first_part = merged_info['data_parts'][0]
                        file_name = first_part.get('file_name', '')
                        shop_name = extract_shop_name(file_name)

                        # 添加店铺名称到sheet名称中
                        if shop_name and shop_name != '未知店铺':
                            # 限制店铺名称长度，避免sheet名称过长
                            max_shop_name_length = 8  # 限制店铺名称最大长度
                            short_shop_name = shop_name[:max_shop_name_length]
                            final_name = f"{short_shop_name}-{base_name}({data_count})"
                        else:
                            final_name = f"{base_name}({data_count})"

                        # 确保工作表名称不超过31个字符
                        if len(final_name) > 31:
                            # 如果太长，缩短基础名称
                            max_base_length = 31 - len(f"({data_count})") - 1  # 减去括号和数字的长度
                            if shop_name and shop_name != '未知店铺':
                                # 如果有店铺名称，考虑店铺名称和分隔符的长度
                                shop_name_part = short_shop_name + "-"
                                max_base_length = max_base_length - len(shop_name_part)
                                if max_base_length > 0:
                                    final_name = f"{short_shop_name}-{base_name[:max_base_length]}({data_count})"
                                else:
                                    # 如果空间不够，只保留店铺名称和数量
                                    final_name = f"{short_shop_name}({data_count})"
                            else:
                                # 没有店铺名称的情况
                                if max_base_length > 0:
                                    final_name = f"{base_name[:max_base_length]}({data_count})"
                                else:
                                    final_name = f"Sheet({data_count})"

                        # 更新merged_info中的sheet_name
                        merged_info['sheet_name'] = final_name
                        clean_name = final_name

                        # 创建新的工作表
                        ws = wb.create_sheet(title=clean_name)

                        # 复制原始文件的样式和数据（使用第一个部分的样式信息）
                        first_part = merged_info['data_parts'][0]
                        self.copy_with_style(first_part, ws, combined_data)

                        sheet_created = True
                        total_rows += merged_info['total_rows']

                        # 收集汇总数据
                        phone_names = list(merged_info['phone_names']) if merged_info['phone_names'] else ['未分组']
                        phone_display = ', '.join(sort_by_pinyin(phone_names)) if phone_names != ['未分组'] else '未分组'
                        
                        # 从第一个数据部分获取文件名，提取店铺名称
                        first_part = merged_info['data_parts'][0]
                        file_name = first_part.get('file_name', '')
                        shop_name = extract_shop_name(file_name)

                        summary_data.append({
                            'sheet_name': clean_name,
                            'base_operation_code': base_operation_code,
                            'phone_name': phone_display,
                            'data_count': merged_info['total_rows'],
                            'shop_name': shop_name
                        })

                # 创建汇总Sheet（放在第一个位置）
                if summary_data:
                    # 数据已经在上面合并了，直接创建汇总Sheet
                    summary_ws = wb.create_sheet(title="分配汇总", index=0)
                    self.create_summary_sheet(summary_ws, summary_data)
                    sheet_created = True

                if sheet_created:
                    wb.save(staff_file)
                    self.emit_status(f"已为 {staff_name} 创建汇总文件: {total_rows} 条评价，{len(staff_operations)} 个运营编码")
                else:
                    self.emit_status(f"警告: {staff_name} 没有有效评价，跳过文件创建")

        # 处理未匹配的数据（总是创建文件）
        self.create_unmatched_file(unmatched_data, output_path)

        # 生成客服分配总量统计表
        if staff_data:
            self.generate_staff_allocation_summary(staff_data, output_path)

        self.emit_status("所有Excel文件生成完成")

    def generate_excel_files_with_daily_work(self, allocated_data, output_path, unmatched_data=None,
                                           staff_operation_codes=None, daily_work_df=None, operation_data=None):
        """生成Excel文件并包含每日工作总表"""
        # 先生成常规的Excel文件
        self.generate_excel_files(allocated_data, output_path, unmatched_data)

        # 生成每个客服的每日工作总表
        if staff_operation_codes and daily_work_df is not None:
            self.generate_daily_work_tables(output_path, staff_operation_codes, daily_work_df, operation_data)

    def copy_with_style(self, info, ws, data_df):
        """复制数据并尽可能保持原始样式"""
        try:
            # 尝试从原始文件复制样式
            if self.copy_original_style(info, ws, data_df):
                return

            # 如果原始样式复制失败，应用基本格式化
            self.write_without_style(ws, data_df)
            self.apply_basic_formatting(ws, data_df)

        except Exception as e:
            # 如果出现任何错误，使用最简单的写入方式
            self.emit_status(f"格式化失败，使用纯数据格式: {str(e)}")
            self.write_without_style(ws, data_df)

    def write_without_style(self, target_ws, data_df):
        """不带样式的普通写入方式 - 性能优化版本"""
        from openpyxl.utils.dataframe import dataframe_to_rows

        # 性能优化：批量写入而不是逐个单元格
        rows_data = list(dataframe_to_rows(data_df, index=False, header=False))

        # 使用append方法批量添加行，比逐个单元格写入快得多
        for row in rows_data:
            target_ws.append(row)

    def copy_original_style(self, info, ws, data_df):
        """尝试复制原始文件的样式"""
        try:
            from openpyxl import load_workbook
            from openpyxl.styles import Font, Border, Side, Alignment, PatternFill

            # 获取原始文件路径
            original_file = info.get('file_path')
            if not original_file or not os.path.exists(original_file):
                self.emit_status(f"原始文件不存在: {original_file}")
                return False

            self.emit_status(f"开始复制样式从: {original_file}")

            # 加载原始工作簿
            original_wb = load_workbook(original_file)
            original_ws = original_wb.active

            self.emit_status(f"原始文件尺寸: {original_ws.max_row}行 x {original_ws.max_column}列")

            # 复制数据和样式
            styles_copied = 0
            for row_idx, (_, row) in enumerate(data_df.iterrows()):
                for col_idx, value in enumerate(row):
                    target_cell = ws.cell(row=row_idx + 1, column=col_idx + 1, value=value)

                    # 如果原始文件中有对应的单元格，复制其样式
                    if row_idx < original_ws.max_row and col_idx < original_ws.max_column:
                        original_cell = original_ws.cell(row=row_idx + 1, column=col_idx + 1)

                        # 调试：检查原始单元格的样式
                        if row_idx == 0 and col_idx == 0:  # 只检查第一个单元格
                            self.emit_status(f"原始单元格A1样式检查:")
                            if original_cell.font:
                                self.emit_status(f"  字体: {original_cell.font.name}, 大小: {original_cell.font.size}, 加粗: {original_cell.font.bold}")
                            if original_cell.fill:
                                self.emit_status(f"  填充: {original_cell.fill.fill_type}, 颜色: {original_cell.fill.start_color}")

                        # 强化字体复制
                        try:
                            font_applied = False
                            if original_cell.font:
                                font_name = original_cell.font.name or '微软雅黑'
                                font_size = original_cell.font.size or 10
                                font_bold = bool(original_cell.font.bold) if original_cell.font.bold is not None else False
                                font_italic = bool(original_cell.font.italic) if original_cell.font.italic is not None else False

                                target_cell.font = Font(
                                    name=font_name,
                                    size=font_size,
                                    bold=font_bold,
                                    italic=font_italic,
                                    color=original_cell.font.color
                                )
                                font_applied = True

                            if not font_applied:
                                target_cell.font = Font(name='微软雅黑', size=10)
                        except Exception as e:
                            target_cell.font = Font(name='微软雅黑', size=10)

                        # 强化填充色复制
                        try:
                            if original_cell.fill and original_cell.fill.fill_type:
                                if original_cell.fill.fill_type == 'solid' and original_cell.fill.start_color:
                                    # 尝试获取颜色值
                                    color_value = None
                                    color_source = ""

                                    # 方法1: 直接RGB值
                                    if hasattr(original_cell.fill.start_color, 'rgb') and original_cell.fill.start_color.rgb:
                                        color_value = original_cell.fill.start_color.rgb
                                        color_source = "RGB"

                                    # 方法2: 索引颜色
                                    elif hasattr(original_cell.fill.start_color, 'indexed') and original_cell.fill.start_color.indexed is not None:
                                        # Excel标准索引颜色映射（基于Excel 2007+标准调色板）
                                        indexed_colors = {
                                            0: 'FF000000',  # 黑色
                                            1: 'FFFFFFFF',  # 白色
                                            2: 'FFFF0000',  # 红色
                                            3: 'FF00FF00',  # 绿色
                                            4: 'FF0000FF',  # 蓝色
                                            5: 'FFFFFF00',  # 黄色
                                            6: 'FFFF00FF',  # 洋红
                                            7: 'FF00FFFF',  # 青色
                                            8: 'FF000000',  # 黑色
                                            9: 'FFFFFFFF',  # 白色
                                            10: 'FFFF0000', # 红色
                                            11: 'FF00FF00', # 绿色
                                            12: 'FF0000FF', # 蓝色
                                            13: 'FFFFFF00', # 黄色
                                            14: 'FFFF00FF', # 洋红
                                            15: 'FF00FFFF', # 青色
                                            16: 'FF800000', # 深红
                                            17: 'FF008000', # 深绿
                                            18: 'FF000080', # 深蓝
                                            19: 'FF808000', # 橄榄色
                                            20: 'FF800080', # 紫色
                                            21: 'FF008080', # 青绿色
                                            22: 'FFC0C0C0', # 银色
                                            23: 'FF808080', # 灰色
                                            24: 'FF9999FF', # 浅蓝
                                            25: 'FF993366', # 深粉
                                            26: 'FFFFFFCC', # 浅黄
                                            27: 'FFCCFFFF', # 浅青
                                            28: 'FF660066', # 深紫
                                            29: 'FFFF8080', # 浅红
                                            30: 'FF0066CC', # 蓝色
                                            31: 'FFCCCCFF', # 浅紫
                                            32: 'FF000080', # 深蓝
                                            33: 'FFFF00FF', # 洋红
                                            34: 'FFFFFF00', # 黄色 - 这是您的运营表格使用的颜色！
                                            35: 'FF00FFFF', # 青色
                                            36: 'FF800080', # 紫色
                                            37: 'FF800000', # 深红
                                            38: 'FF008080', # 青绿
                                            39: 'FF0000FF', # 蓝色
                                            40: 'FF00CCFF', # 天蓝
                                            41: 'FFCCFFFF', # 浅青
                                            42: 'FFCCFFCC', # 浅绿
                                            43: 'FFFFFF99', # 浅黄
                                            44: 'FF99CCFF', # 浅蓝
                                            45: 'FFFF99CC', # 浅粉
                                            46: 'FFCC99FF', # 浅紫
                                            47: 'FFFFCC99', # 浅橙
                                            48: 'FF3366FF', # 蓝色
                                            49: 'FF33CCCC', # 青色
                                            50: 'FF99CC00', # 绿黄
                                            51: 'FFFFCC00', # 金黄
                                            52: 'FFFF9900', # 橙色
                                            53: 'FFFF6600', # 深橙
                                            54: 'FF666699', # 蓝灰
                                            55: 'FF969696', # 灰色
                                            56: 'FF003366', # 深蓝
                                        }
                                        idx = original_cell.fill.start_color.indexed
                                        color_value = indexed_colors.get(idx, 'FFFFFF00')  # 默认黄色
                                        color_source = f"索引{idx}"

                                    # 方法3: 主题颜色
                                    elif hasattr(original_cell.fill.start_color, 'theme') and original_cell.fill.start_color.theme is not None:
                                        # 主题颜色通常需要从工作簿的主题中解析，这里使用常见的黄色
                                        color_value = 'FFFFFF00'  # 默认黄色
                                        color_source = f"主题{original_cell.fill.start_color.theme}"

                                    # 方法4: 如果都没有，但有填充，使用默认黄色
                                    else:
                                        color_value = 'FFFFFF00'  # 默认黄色
                                        color_source = "默认"

                                    # 应用颜色
                                    if color_value and color_value != '00000000':  # 不是透明色
                                        # 确保颜色值格式正确
                                        if len(color_value) == 6:
                                            color_value = 'FF' + color_value  # 添加Alpha通道
                                        elif len(color_value) == 8 and not color_value.startswith('FF'):
                                            # 如果是8位但不以FF开头，可能需要调整
                                            pass

                                        target_cell.fill = PatternFill(
                                            start_color=color_value,
                                            end_color=color_value,
                                            fill_type='solid'
                                        )
                                        if row_idx == 0 and col_idx == 0:
                                            self.emit_status(f"  应用背景色: {color_value} (来源: {color_source})")
                        except Exception as e:
                            if row_idx == 0 and col_idx == 0:
                                self.emit_status(f"  背景色复制失败: {e}")
                            # 如果复制失败，尝试应用默认黄色
                            try:
                                target_cell.fill = PatternFill(
                                    start_color='FFFFFF00',
                                    end_color='FFFFFF00',
                                    fill_type='solid'
                                )
                                if row_idx == 0 and col_idx == 0:
                                    self.emit_status(f"  应用默认黄色背景")
                            except:
                                pass

                        # 强化边框复制
                        try:
                            if original_cell.border:
                                left_style = original_cell.border.left.style if original_cell.border.left else None
                                right_style = original_cell.border.right.style if original_cell.border.right else None
                                top_style = original_cell.border.top.style if original_cell.border.top else None
                                bottom_style = original_cell.border.bottom.style if original_cell.border.bottom else None

                                if any([left_style, right_style, top_style, bottom_style]):
                                    target_cell.border = Border(
                                        left=Side(style=left_style or 'thin'),
                                        right=Side(style=right_style or 'thin'),
                                        top=Side(style=top_style or 'thin'),
                                        bottom=Side(style=bottom_style or 'thin')
                                    )
                        except:
                            pass

                        # 强化对齐复制
                        try:
                            if original_cell.alignment:
                                target_cell.alignment = Alignment(
                                    horizontal=original_cell.alignment.horizontal or 'left',
                                    vertical=original_cell.alignment.vertical or 'center',
                                    wrap_text=original_cell.alignment.wrap_text or False
                                )
                        except:
                            pass

                        styles_copied += 1

            self.emit_status(f"样式复制完成: {styles_copied} 个单元格")

            # 复制列宽
            try:
                for col_idx in range(1, min(original_ws.max_column + 1, len(data_df.columns) + 1)):
                    col_letter = original_ws.cell(row=1, column=col_idx).column_letter
                    if original_ws.column_dimensions[col_letter].width:
                        ws.column_dimensions[col_letter].width = original_ws.column_dimensions[col_letter].width
            except:
                pass

            # 复制行高
            try:
                for row_idx in range(1, min(original_ws.max_row + 1, len(data_df) + 1)):
                    if original_ws.row_dimensions[row_idx].height:
                        ws.row_dimensions[row_idx].height = original_ws.row_dimensions[row_idx].height
            except:
                pass

            return True

        except Exception as e:
            self.emit_status(f"原始样式复制失败: {e}")
            return False

    def apply_basic_formatting(self, ws, data_df):
        """应用基本格式化"""
        try:
            from openpyxl.styles import Font, Border, Side, Alignment

            # 基本字体
            basic_font = Font(name='微软雅黑', size=10)
            header_font = Font(name='微软雅黑', size=11, bold=True)

            # 基本边框
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # 基本对齐 - 所有内容都居中显示
            center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

            # 应用格式到所有单元格
            for row_idx in range(1, len(data_df) + 1):
                for col_idx in range(1, len(data_df.columns) + 1):
                    cell = ws.cell(row=row_idx, column=col_idx)

                    # 前两行使用表头样式
                    if row_idx <= 2:
                        cell.font = header_font
                        cell.alignment = center_alignment
                        # 为表头添加背景色
                        from openpyxl.styles import PatternFill
                        cell.fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')
                    else:
                        cell.font = basic_font
                        cell.alignment = center_alignment  # 数据行也居中显示

                    cell.border = thin_border

            # 设置基本列宽
            for col_idx in range(1, min(len(data_df.columns) + 1, 10)):
                col_letter = ws.cell(row=1, column=col_idx).column_letter
                ws.column_dimensions[col_letter].width = 15

        except Exception as e:
            # 如果基本格式化也失败，就不应用任何格式
            pass

    def create_summary_sheet(self, summary_ws, summary_data):
        """创建汇总Sheet"""
        # 设置样式
        header_font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
        data_font = Font(name='微软雅黑', size=10)
        link_font = Font(name='微软雅黑', size=10, color='0000FF', underline='single')

        header_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        data_border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        header_alignment = Alignment(horizontal='center', vertical='center')
        data_alignment = Alignment(horizontal='center', vertical='center')  # 改为居中对齐

        # 设置标题
        headers = ['运营编码', '店铺名称', '手机名称', '评价量', '跳转链接']

        for col_idx, header in enumerate(headers, 1):
            cell = summary_ws.cell(row=1, column=col_idx, value=header)
            cell.font = header_font
            cell.border = header_border
            cell.alignment = header_alignment
            # 设置表头背景色
            from openpyxl.styles import PatternFill
            cell.fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')

        # 在K列添加手机名称唯一值标题
        k_col_header = summary_ws.cell(row=1, column=11, value='手机名称汇总')
        k_col_header.font = header_font
        k_col_header.border = header_border
        k_col_header.alignment = header_alignment
        k_col_header.fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')

        # 按店铺名称的拼音进行A-Z排序
        sorted_summary_data = sort_by_pinyin(summary_data, key_func=lambda x: x.get('shop_name', '未知店铺'))

        # 写入数据行
        for row_idx, data in enumerate(sorted_summary_data, 2):
            # 运营编码
            cell = summary_ws.cell(row=row_idx, column=1, value=data['base_operation_code'])
            cell.font = data_font
            cell.border = data_border
            cell.alignment = data_alignment

            # 店铺名称
            shop_name = data.get('shop_name', '未知店铺')
            cell = summary_ws.cell(row=row_idx, column=2, value=shop_name)
            cell.font = data_font
            cell.border = data_border
            cell.alignment = data_alignment

            # 手机名称
            cell = summary_ws.cell(row=row_idx, column=3, value=data['phone_name'])
            cell.font = data_font
            cell.border = data_border
            cell.alignment = data_alignment

            # 评价量
            cell = summary_ws.cell(row=row_idx, column=4, value=data['data_count'])
            cell.font = data_font
            cell.border = data_border
            cell.alignment = data_alignment

            # 超链接
            sheet_name = data['sheet_name']
            cell = summary_ws.cell(row=row_idx, column=5, value=f"跳转到 {sheet_name}")
            cell.font = link_font
            cell.border = data_border
            cell.alignment = data_alignment

            # 创建内部超链接 - 使用正确的openpyxl格式
            cell.hyperlink = Hyperlink(ref="", location=f"'{sheet_name}'!A1")

        # 在K列添加手机名称的唯一值
        self.add_unique_phone_names_to_k_column(summary_ws, sorted_summary_data, data_font, data_border, data_alignment)

        # 自动调整列宽
        self.auto_adjust_column_width(summary_ws, sorted_summary_data)

        # 冻结首行
        summary_ws.freeze_panes = 'A2'

    def add_unique_phone_names_to_k_column(self, summary_ws, summary_data, data_font, data_border, data_alignment):
        """在K列添加分配汇总表中手机名称的唯一值，每个手机名称占一行"""
        # 从分配汇总表的数据中收集手机名称（即summary_data中的phone_name字段）
        all_phone_names = set()
        for data in summary_data:
            phone_name = data.get('phone_name', '')
            if phone_name and phone_name not in ('未知手机', '', '未分组'):
                # 处理可能的多个手机名称（用逗号分隔）
                if ',' in phone_name:
                    phone_names = [name.strip() for name in phone_name.split(',')]
                    # 过滤掉空字符串或无效值
                    phone_names = [name for name in phone_names if name and name not in ('未知手机', '', '未分组')]
                    all_phone_names.update(phone_names)
                else:
                    all_phone_names.add(phone_name.strip())

        # 将手机名称按拼音A-Z排序并写入K列
        unique_phone_names = sort_by_pinyin(list(all_phone_names))

        for row_idx, phone_name in enumerate(unique_phone_names, 2):  # 从第2行开始（第1行是标题）
            cell = summary_ws.cell(row=row_idx, column=11, value=phone_name)  # K列是第11列
            cell.font = data_font
            cell.border = data_border
            cell.alignment = data_alignment

        # 如果没有手机名称，显示提示信息
        if not unique_phone_names:
            cell = summary_ws.cell(row=2, column=11, value='无手机数据')
            cell.font = data_font
            cell.border = data_border
            cell.alignment = data_alignment

    def create_unmatched_file(self, unmatched_data, output_path):
        """创建增强的未匹配数据分析报告"""
        output_path = Path(output_path)
        unmatched_file = output_path / "1.未匹配数据分析报告.xlsx"

        if unmatched_data:
            # 创建详细的未匹配数据分析
            self.create_detailed_unmatched_report(unmatched_data, unmatched_file)
            self.emit_status(f"已创建未匹配数据分析报告: {len(unmatched_data)} 条数据")
        else:
            # 创建成功匹配的报告
            self.create_success_report(unmatched_file)
            self.emit_status("已创建匹配成功报告")

    def generate_daily_work_tables(self, output_path, staff_operation_codes, daily_work_df, operation_data=None):
        """为每个客服生成对应的每日工作总表 - 只包含分配给该客服的运营编码数据"""
        self.emit_status("开始生成每日工作总表...")

        # 确保output_path是Path对象
        output_path = Path(output_path)

        # 检查每日工作总表是否有运营编码列
        if '运营编码' not in daily_work_df.columns:
            self.emit_status("警告: 每日工作总表中未找到'运营编码'列，跳过每日工作总表生成")
            return
            
        # 预处理每日工作总表的运营编码，添加纯数字编码列用于匹配
        self.emit_status("预处理每日工作总表，提取纯数字编码...")
        daily_work_df['运营编码_纯数字'] = daily_work_df['运营编码'].astype(str).apply(self.extract_digits_from_code)
        self.emit_status(f"每日工作总表纯数字编码提取完成，共 {len(daily_work_df)} 条数据")

        for staff_name, operation_codes in staff_operation_codes.items():
            if not operation_codes:  # 如果该客服没有分配到任何运营编码
                continue

            # 提取原始运营编码和对应的手机信息，并获取对应的每日工作编码
            original_operation_codes = set()
            daily_work_codes = set()  # 用于在每日工作总表中查找的编码
            daily_work_digit_codes = set()  # 用于存储纯数字编码
            staff_phones = set()

            for code in operation_codes:
                if '_手机_' in code:
                    # 分割后的运营编码，提取原始部分和手机名称
                    parts = code.split('_手机_')
                    if len(parts) >= 2:
                        original_code = parts[0]
                        phone_name = parts[1]
                        original_operation_codes.add(original_code)
                        staff_phones.add(phone_name)

                        # 获取对应的每日工作编码
                        daily_codes = self.get_daily_work_codes_for_operation(original_code, operation_data)
                        daily_work_codes.update(daily_codes)
                        
                        # 同时提取纯数字编码
                        for daily_code in daily_codes:
                            digit_code = self.extract_digits_from_code(daily_code)
                            if digit_code:
                                daily_work_digit_codes.add(digit_code)
                else:
                    # 普通运营编码
                    original_operation_codes.add(code)

                    # 获取对应的每日工作编码
                    daily_codes = self.get_daily_work_codes_for_operation(code, operation_data)
                    daily_work_codes.update(daily_codes)
                    
                    # 同时提取纯数字编码
                    for daily_code in daily_codes:
                        digit_code = self.extract_digits_from_code(daily_code)
                        if digit_code:
                            daily_work_digit_codes.add(digit_code)

            self.emit_status(f"为 {staff_name} 匹配每日工作数据，原始运营编码: {list(original_operation_codes)}")
            self.emit_status(f"  对应的每日工作编码: {list(daily_work_codes)}")
            self.emit_status(f"  对应的纯数字编码: {list(daily_work_digit_codes)}")
            if staff_phones:
                self.emit_status(f"  对应手机: {list(staff_phones)}")

            # 优化筛选策略：先按运营编码筛选，再考虑手机名称
            # 第一步：按运营编码筛选（原始编码或纯数字编码）
            staff_daily_data = daily_work_df[
                (daily_work_df['运营编码'].isin(daily_work_codes)) | 
                (daily_work_df['运营编码_纯数字'].isin(daily_work_digit_codes))
            ].copy()
            self.emit_status(f"  按运营编码(含纯数字)筛选: {len(staff_daily_data)} 条数据")
            
            # 第二步：如果有手机信息，进一步优化结果（但不强制过滤）
            if staff_phones and '手机名称' in daily_work_df.columns and len(staff_daily_data) > 0:
                # 找出同时匹配手机名称的数据
                matching_phone_data = staff_daily_data[staff_daily_data['手机名称'].isin(staff_phones)]
                
                if len(matching_phone_data) > 0:
                    # 如果有匹配手机名称的数据，优先使用这部分数据
                    self.emit_status(f"  其中匹配手机名称的数据: {len(matching_phone_data)} 条")
                    
                    # 检查是否有运营编码完全丢失（只在手机名称不匹配的数据中）
                    missing_codes = set()
                    for code in daily_work_codes:
                        if code in staff_daily_data['运营编码'].values and code not in matching_phone_data['运营编码'].values:
                            missing_codes.add(code)
                    
                    if missing_codes:
                        # 如果有运营编码在手机名称筛选后丢失，只添加这些编码中属于该客服手机的数据
                        self.emit_status(f"  发现 {len(missing_codes)} 个运营编码在手机名称筛选后丢失")
                        missing_data = staff_daily_data[staff_daily_data['运营编码'].isin(missing_codes)]

                        # 进一步筛选：只保留属于该客服手机的数据，或者手机名称为空的数据
                        if '手机名称' in missing_data.columns:
                            # 保留手机名称匹配或为空的数据
                            filtered_missing_data = missing_data[
                                (missing_data['手机名称'].isin(staff_phones)) |
                                (missing_data['手机名称'].isna()) |
                                (missing_data['手机名称'] == '') |
                                (missing_data['手机名称'].str.strip() == '')
                            ]
                            self.emit_status(f"  从丢失编码中筛选出 {len(filtered_missing_data)} 条相关数据")
                            staff_daily_data = pd.concat([matching_phone_data, filtered_missing_data]).drop_duplicates()
                        else:
                            # 如果没有手机名称列，保留所有丢失编码的数据
                            staff_daily_data = pd.concat([matching_phone_data, missing_data]).drop_duplicates()

                        self.emit_status(f"  合并后的数据量: {len(staff_daily_data)} 条")
                    else:
                        # 如果没有丢失的编码，使用匹配手机名称的数据
                        staff_daily_data = matching_phone_data
                else:
                    # 没有匹配手机名称的数据，但仍然需要筛选出相关数据
                    self.emit_status(f"  警告：没有同时匹配手机名称的数据")

                    # 尝试保留手机名称为空或未知的数据（可能是该客服负责但手机信息缺失的数据）
                    if '手机名称' in staff_daily_data.columns:
                        empty_phone_data = staff_daily_data[
                            (staff_daily_data['手机名称'].isna()) |
                            (staff_daily_data['手机名称'] == '') |
                            (staff_daily_data['手机名称'].str.strip() == '') |
                            (staff_daily_data['手机名称'] == '未知手机')
                        ]

                        if len(empty_phone_data) > 0:
                            self.emit_status(f"  保留 {len(empty_phone_data)} 条手机名称为空的数据")
                            staff_daily_data = empty_phone_data
                        else:
                            self.emit_status(f"  没有找到相关数据，跳过该客服的每日工作表生成")
                            staff_daily_data = pd.DataFrame()  # 设置为空DataFrame
                    else:
                        self.emit_status(f"  保留所有匹配运营编码的数据")

            if len(staff_daily_data) > 0:
                # 移除临时列
                if '运营编码_纯数字' in staff_daily_data.columns:
                    staff_daily_data = staff_daily_data.drop(columns=['运营编码_纯数字'])

                # 生成该客服的每日工作总表，处理订单编号格式问题
                # 获取明天的日期，格式为_MM.dd
                tomorrow = datetime.now() + timedelta(days=1)
                current_date = tomorrow.strftime("_%m.%d")
                daily_work_file = output_path / f"{staff_name}_每日工作总表{current_date}.xlsx"

                # 使用openpyxl引擎并设置格式
                with pd.ExcelWriter(daily_work_file, engine='openpyxl') as writer:
                    staff_daily_data.to_excel(writer, sheet_name='每日工作数据', index=False)

                    # 获取工作表对象
                    worksheet = writer.sheets['每日工作数据']

                    # 应用完整的格式化（包括居中对齐）
                    self.format_daily_work_table(worksheet, staff_daily_data)

                    # 添加该客服的手机名称汇总到右侧列
                    self.add_staff_phone_summary_to_daily_work(worksheet, staff_phones, staff_daily_data)

                # 详细显示分配的运营编码
                assigned_codes = list(operation_codes)
                self.emit_status(f"已为 {staff_name} 创建每日工作总表: {len(staff_daily_data)} 条数据")
                self.emit_status(f"  包含运营编码: {assigned_codes}")
            else:
                # 如果没有匹配的数据，创建空表格
                empty_df = pd.DataFrame({"说明": [f"{staff_name} 没有匹配的每日工作数据"]})
                # 获取明天的日期，格式为_MM.dd
                tomorrow = datetime.now() + timedelta(days=1)
                current_date = tomorrow.strftime("_%m.%d")
                daily_work_file = output_path / f"{staff_name}_每日工作总表{current_date}.xlsx"
                empty_df.to_excel(daily_work_file, index=False)

                self.emit_status(f"已为 {staff_name} 创建空的每日工作总表")

        self.emit_status("每日工作总表生成完成")

    def add_staff_phone_summary_to_daily_work(self, worksheet, staff_phones, staff_daily_data):
        """在每日工作表右侧添加该客服的手机名称汇总"""
        try:
            from openpyxl.styles import Font, Border, Side, Alignment, PatternFill

            # 如果没有手机信息，不添加汇总列
            if not staff_phones:
                return

            # 找到合适的列位置（在现有数据的右侧）
            last_col = len(staff_daily_data.columns) + 2  # +2 留一列空隙

            # 定义样式
            header_font = Font(name='微软雅黑', size=11, bold=True, color='FFFFFF')
            data_font = Font(name='微软雅黑', size=10)
            center_alignment = Alignment(horizontal='center', vertical='center')
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            header_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')

            # 添加标题
            header_cell = worksheet.cell(row=1, column=last_col, value='分配手机汇总')
            header_cell.font = header_font
            header_cell.fill = header_fill
            header_cell.alignment = center_alignment
            header_cell.border = thin_border

            # 添加手机名称（按拼音A-Z排序）
            sorted_phones = sort_by_pinyin(list(staff_phones))
            for row_idx, phone_name in enumerate(sorted_phones, 2):  # 从第2行开始
                cell = worksheet.cell(row=row_idx, column=last_col, value=phone_name)
                cell.font = data_font
                cell.alignment = center_alignment
                cell.border = thin_border

            # 设置列宽
            col_letter = self.get_column_letter(last_col)
            worksheet.column_dimensions[col_letter].width = 15

            self.emit_status(f"已添加手机名称汇总列，包含 {len(sorted_phones)} 个手机")

        except Exception as e:
            self.emit_status(f"添加手机名称汇总列失败: {e}")

    def get_daily_work_codes_for_operation(self, operation_code, operation_data):
        """
        获取运营编码对应的每日工作总表编码，增强匹配能力

        Args:
            operation_code: 运营表格中的编码（可能是纯数字）
            operation_data: 运营数据字典

        Returns:
            set: 对应的每日工作总表编码集合
        """
        result_codes = set()
        
        # 第一步：添加原始编码
        result_codes.add(operation_code)
        
        # 第二步：检查是否有二次匹配信息
        if operation_data and operation_code in operation_data:
            op_info = operation_data[operation_code]
            
            if 'secondary_match' in op_info:
                match_info = op_info['secondary_match']
                if 'all_matches' in match_info:
                    # 有多个匹配，添加所有匹配
                    result_codes.update(set(match_info['all_matches']))
                else:
                    # 只有一个匹配
                    result_codes.add(match_info['matched_code'])
        
        # 第三步：处理特殊格式的编码
        # 如果编码中包含"_手机_"，提取基础编码部分
        if '_手机_' in operation_code:
            base_code = operation_code.split('_手机_')[0]
            result_codes.add(base_code)
            
            # 如果基础编码也在运营数据中，检查其二次匹配信息
            if operation_data and base_code in operation_data and base_code != operation_code:
                base_op_info = operation_data[base_code]
                if 'secondary_match' in base_op_info:
                    base_match_info = base_op_info['secondary_match']
                    if 'all_matches' in base_match_info:
                        result_codes.update(set(base_match_info['all_matches']))
                    else:
                        result_codes.add(base_match_info['matched_code'])
        
        # 第四步：提取纯数字编码（用于后续匹配）
        # 注意：这里不直接添加纯数字编码，而是在调用方使用
        
        self.emit_status(f"运营编码 '{operation_code}' 对应的每日工作编码: {list(result_codes)}")
        return result_codes

    def create_detailed_unmatched_report(self, unmatched_data, file_path):
        """创建简化的未匹配数据报告"""
        try:
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # 只创建详细数据Sheet，包含指定的列
                self.create_simplified_unmatched_sheet(unmatched_data, writer)

        except Exception as e:
            self.emit_status(f"创建未匹配数据报告失败: {e}")
            # 如果失败，创建简单版本
            simple_df = pd.DataFrame(unmatched_data)
            simple_df.to_excel(file_path, index=False)

    def create_simplified_unmatched_sheet(self, unmatched_data, writer):
        """创建简化的未匹配数据Sheet，只包含必要的列"""
        # 提取需要的字段
        simplified_data = []
        for i, item in enumerate(unmatched_data, 1):
            # 提取运营编码
            operation_code = item.get('运营编码', self.extract_operation_code_from_data(item))

            # 提取主评和追评（从原始数据的不同可能字段中）
            # 运营表格使用数字索引作为列名，通常第0列是主评，第1列是追评
            main_review = self.extract_field_value(item, [0, '主评', '评价内容', '商品名称'])
            follow_review = self.extract_field_value(item, [1, '追评', '追评内容'])

            # 提取原始文件路径
            original_file = item.get('原始文件', item.get('file_path', '未知文件'))
            
            # 提取未匹配原因
            unmatch_reason = item.get('未匹配原因', '未知原因')
            
            # 提取店铺名称
            file_name = Path(original_file).stem if original_file else ""
            shop_name = extract_shop_name(file_name)

            simplified_item = {
                '序号': i,
                '运营编码': operation_code,
                '店铺名称': shop_name,  # 添加店铺名称列
                '未匹配原因': unmatch_reason,
                '主评': main_review,
                '追评': follow_review,
                '原始文件': original_file
            }

            simplified_data.append(simplified_item)

        # 创建DataFrame并写入Excel
        simplified_df = pd.DataFrame(simplified_data)
        simplified_df.to_excel(writer, sheet_name='未匹配数据', index=False)

        # 格式化表格
        worksheet = writer.sheets['未匹配数据']
        self.format_simplified_unmatched_sheet(worksheet, simplified_df)

    def extract_field_value(self, data_item, possible_fields):
        """从数据项中提取字段值，尝试多个可能的字段名（包括数字索引）"""
        for field in possible_fields:
            if field in data_item and data_item[field] is not None:
                value = str(data_item[field]).strip()
                if value and value != 'nan' and value != '':
                    return value
        return ''

    def format_simplified_unmatched_sheet(self, worksheet, data_df):
        """格式化简化的未匹配数据表"""
        try:
            from openpyxl.styles import Font, Border, Side, Alignment, PatternFill

            # 定义样式
            header_font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
            data_font = Font(name='微软雅黑', size=10)
            center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
            left_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)

            # 定义颜色
            header_fill = PatternFill(start_color='DC143C', end_color='DC143C', fill_type='solid')  # 深红色表头
            reason_fill = PatternFill(start_color='FFC7CE', end_color='FFC7CE', fill_type='solid')  # 浅红色未匹配原因
            shop_fill = PatternFill(start_color='E2EFDA', end_color='E2EFDA', fill_type='solid')  # 浅绿色店铺名称
            thin_border = Border(
                left=Side(style='thin'), right=Side(style='thin'),
                top=Side(style='thin'), bottom=Side(style='thin')
            )

            # 格式化表头
            for col in range(1, len(data_df.columns) + 1):
                cell = worksheet.cell(row=1, column=col)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = center_alignment
                cell.border = thin_border

            # 格式化数据行
            for row in range(2, len(data_df) + 2):
                for col in range(1, len(data_df.columns) + 1):
                    cell = worksheet.cell(row=row, column=col)
                    cell.font = data_font
                    cell.border = thin_border
                    
                    # 设置对齐方式：序号、运营编码、店铺名称和未匹配原因居中，其他左对齐
                    col_name = list(data_df.columns)[col-1]
                    if col_name in ['序号', '运营编码', '店铺名称', '未匹配原因']:
                        cell.alignment = center_alignment
                    else:
                        cell.alignment = left_alignment
                    
                    # 为未匹配原因列添加特殊背景色
                    if col_name == '未匹配原因':
                        cell.fill = reason_fill
                    # 为店铺名称列添加特殊背景色
                    elif col_name == '店铺名称':
                        cell.fill = shop_fill

            # 自动调整列宽
            self.auto_adjust_unmatched_column_widths(worksheet, data_df)

            # 设置行高
            for row in range(1, len(data_df) + 2):
                worksheet.row_dimensions[row].height = 30  # 增加行高以便显示多行内容

        except Exception as e:
            self.emit_status(f"格式化简化未匹配表失败: {e}")

    def auto_adjust_unmatched_column_widths(self, worksheet, data_df):
        """自动调整未匹配数据表的列宽"""
        try:
            # 定义列的最小和最大宽度
            column_min_widths = {
                '序号': 6,
                '运营编码': 15,
                '店铺名称': 12,
                '未匹配原因': 18,
                '主评': 30,
                '追评': 30,
                '原始文件': 25
            }
            
            column_max_widths = {
                '序号': 8,
                '运营编码': 25,
                '店铺名称': 20,
                '未匹配原因': 30,
                '主评': 50,
                '追评': 50,
                '原始文件': 40
            }
            
            # 计算每列的最佳宽度
            optimal_widths = {}
            
            # 从表头开始计算
            for i, col_name in enumerate(data_df.columns):
                # 初始宽度为表头长度
                max_length = self.calculate_display_width(str(col_name)) + 2  # +2 为内边距
                
                # 检查所有数据行
                for _, row in data_df.iterrows():
                    cell_value = str(row[col_name]) if pd.notna(row[col_name]) else ""
                    # 计算显示宽度（中文字符占用更多空间）
                    content_length = self.calculate_display_width(cell_value) + 2  # +2 为内边距
                    max_length = max(max_length, content_length)
                
                # 应用最小和最大宽度限制
                min_width = column_min_widths.get(col_name, 10)
                max_width = column_max_widths.get(col_name, 30)
                optimal_width = max(min_width, min(max_length, max_width))
                
                # 存储最佳宽度
                optimal_widths[col_name] = optimal_width
            
            # 应用列宽
            for i, col_name in enumerate(data_df.columns):
                col_letter = worksheet.cell(row=1, column=i+1).column_letter
                worksheet.column_dimensions[col_letter].width = optimal_widths[col_name]
                
        except Exception as e:
            self.emit_status(f"自动调整未匹配数据表列宽失败: {e}")

    def format_daily_work_table(self, worksheet, data_df):
        """格式化每日工作总表，包括居中对齐、文本格式和样式"""
        try:
            from openpyxl.styles import Font, Border, Side, Alignment, PatternFill

            # 定义样式
            header_font = Font(name='微软雅黑', size=11, bold=True, color='FFFFFF')
            data_font = Font(name='微软雅黑', size=10)

            # 定义对齐方式
            center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

            # 定义边框
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # 定义背景色
            header_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')  # 蓝色表头

            # 需要设置为文本格式的列
            text_columns = ['订单编号', '运营编码', '手机名称', '客户名称']

            # 格式化所有单元格
            for row in range(1, len(data_df) + 2):  # +2 因为包括标题行
                for col in range(1, len(data_df.columns) + 1):
                    cell = worksheet.cell(row=row, column=col)

                    # 设置边框
                    cell.border = thin_border

                    # 设置对齐方式（所有单元格都居中）
                    cell.alignment = center_alignment

                    if row == 1:  # 标题行
                        cell.font = header_font
                        cell.fill = header_fill
                    else:  # 数据行
                        cell.font = data_font

                        # 为特定列设置文本格式
                        col_name = data_df.columns[col - 1]
                        if col_name in text_columns:
                            cell.number_format = '@'  # 文本格式

                            # 确保长数字正确显示为字符串
                            data_row_index = row - 2
                            if data_row_index < len(data_df):
                                original_value = data_df.iloc[data_row_index][col_name]
                                if pd.notna(original_value):
                                    cell.value = str(original_value)

            # 自动调整列宽
            self.auto_adjust_column_widths(worksheet, data_df)

            # 设置行高
            for row in range(1, len(data_df) + 2):
                worksheet.row_dimensions[row].height = 25  # 设置行高为25

            self.emit_status("每日工作总表格式化完成：居中对齐、文本格式、自动列宽")

        except Exception as e:
            self.emit_status(f"每日工作总表格式化失败: {e}")
            # 如果格式化失败，至少确保基本的文本格式
            try:
                text_columns = ['订单编号', '运营编码', '手机名称', '客户名称']
                for col_name in text_columns:
                    if col_name in data_df.columns:
                        col_index = data_df.columns.get_loc(col_name) + 1
                        col_letter = self.get_column_letter(col_index)
                        for row in range(1, len(data_df) + 2):
                            cell = worksheet[f'{col_letter}{row}']
                            cell.number_format = '@'
                            if row > 1:
                                data_row_index = row - 2
                                if data_row_index < len(data_df):
                                    original_value = data_df.iloc[data_row_index][col_name]
                                    if pd.notna(original_value):
                                        cell.value = str(original_value)
            except:
                pass

    def get_column_letter(self, col_index):
        """将列索引转换为Excel列字母（1->A, 2->B, ..., 27->AA）"""
        result = ""
        while col_index > 0:
            col_index -= 1
            result = chr(65 + col_index % 26) + result
            col_index //= 26
        return result

    def auto_adjust_column_widths(self, worksheet, data_df):
        """自动调整列宽以适应内容（改进版，支持中文字符）"""
        try:
            from openpyxl.utils import get_column_letter

            # 遍历所有列
            for col_index, column_name in enumerate(data_df.columns, 1):
                col_letter = get_column_letter(col_index)

                # 计算列的最大显示宽度（考虑中文字符）
                max_width = self.calculate_display_width(str(column_name))  # 标题长度

                # 检查数据行的最大宽度
                for value in data_df[column_name]:
                    if pd.notna(value):
                        value_str = str(value)
                        display_width = self.calculate_display_width(value_str)
                        max_width = max(max_width, display_width)

                # 设置合理的列宽限制
                max_width = min(max_width, 60)  # 最大60个显示单位
                max_width = max(max_width, 8)   # 最小8个显示单位

                # 应用列宽（Excel的列宽单位需要调整，中文字符需要更多空间）
                excel_width = max_width * 0.8 + 2  # 添加内边距
                worksheet.column_dimensions[col_letter].width = excel_width

                # 特殊处理一些常见列
                if '订单编号' in column_name or '运营编码' in column_name:
                    # 订单编号和运营编码通常较长，给予更多空间
                    worksheet.column_dimensions[col_letter].width = max(excel_width, 20)
                elif '手机名称' in column_name or '客户名称' in column_name:
                    # 名称类列给予适中空间
                    worksheet.column_dimensions[col_letter].width = max(excel_width, 12)
                elif '商品' in column_name or '标题' in column_name:
                    # 商品相关列可能很长，但限制最大宽度
                    worksheet.column_dimensions[col_letter].width = min(excel_width, 40)

        except Exception as e:
            # 如果自动调整失败，使用默认宽度
            self.emit_status(f"自动调整列宽失败，使用默认宽度: {e}")
            try:
                # 设置一些常见列的默认宽度
                default_widths = {
                    'A': 15,  # 第一列
                    'B': 20,  # 第二列
                    'C': 25,  # 第三列（通常是订单编号）
                    'D': 15,  # 第四列
                    'E': 15,  # 第五列
                    'F': 30,  # 第六列（可能是商品名称）
                    'G': 25,  # 第七列
                    'H': 15,  # 第八列
                    'I': 15,  # 第九列
                    'J': 15,  # 第十列
                }

                for col_letter, width in default_widths.items():
                    try:
                        worksheet.column_dimensions[col_letter].width = width
                    except:
                        pass
            except:
                pass

    def extract_operation_code_from_data(self, data_item):
        """从数据项中提取运营编码"""
        # 尝试从不同的可能字段中提取运营编码
        possible_fields = ['运营编码', 'operation_code', '编码', 'code']

        for field in possible_fields:
            if field in data_item and data_item[field]:
                return str(data_item[field]).strip()

        # 如果没有找到，尝试从文件名或其他信息中提取
        if 'file_path' in data_item:
            file_path = data_item['file_path']
            # 从文件名中提取运营编码（假设格式为 xxx-GL-xxxxxxx-xxx）
            import re
            match = re.search(r'GL-[A-Z0-9]+', str(file_path))
            if match:
                return match.group()

        return '未知运营编码'



    def create_success_report(self, file_path):
        """创建匹配成功的报告"""
        try:
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                success_data = [{
                    '处理结果': '✅ 所有数据都已成功匹配',
                    '匹配状态': '完全匹配',
                    '未匹配数量': 0,
                    '处理时间': pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S'),
                    '说明': '所有运营编码都在每日工作总表中找到了对应记录，数据已成功分配给相应客服'
                }]

                success_df = pd.DataFrame(success_data)
                success_df.to_excel(writer, sheet_name='匹配成功报告', index=False)

                # 格式化成功报告
                worksheet = writer.sheets['匹配成功报告']
                self.format_success_report_sheet(worksheet, success_df)

        except Exception as e:
            # 如果失败，创建简单版本
            simple_df = pd.DataFrame({"说明": ["所有数据都已匹配"]})
            simple_df.to_excel(file_path, index=False)







    def format_success_report_sheet(self, worksheet, data_df):
        """格式化成功报告表"""
        try:
            from openpyxl.styles import Font, Border, Side, Alignment, PatternFill

            # 定义样式
            header_font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
            data_font = Font(name='微软雅黑', size=11, bold=True)
            center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)

            # 定义颜色
            header_fill = PatternFill(start_color='32CD32', end_color='32CD32', fill_type='solid')  # 绿色表头
            success_fill = PatternFill(start_color='F0FFF0', end_color='F0FFF0', fill_type='solid')  # 浅绿色背景
            thin_border = Border(
                left=Side(style='thin'), right=Side(style='thin'),
                top=Side(style='thin'), bottom=Side(style='thin')
            )

            # 格式化表头
            for col in range(1, len(data_df.columns) + 1):
                cell = worksheet.cell(row=1, column=col)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = center_alignment
                cell.border = thin_border

            # 格式化数据行
            for row in range(2, len(data_df) + 2):
                for col in range(1, len(data_df.columns) + 1):
                    cell = worksheet.cell(row=row, column=col)
                    cell.font = data_font
                    cell.fill = success_fill
                    cell.alignment = center_alignment
                    cell.border = thin_border

            # 自动调整列宽
            self.auto_adjust_column_widths(worksheet, data_df)

        except Exception as e:
            self.emit_status(f"格式化成功报告表失败: {e}")

    def auto_adjust_column_width(self, worksheet, summary_data):
        """自动调整汇总Sheet的列宽"""
        try:
            # 定义列标题和最小宽度
            headers = ['运营编码', '店铺名称', '手机名称', '数据量', '跳转链接']
            min_widths = [15, 12, 10, 8, 15]  # 每列的最小宽度
            max_widths = [35, 25, 20, 12, 25]  # 每列的最大宽度

            # 计算每列的最佳宽度
            for col_idx, (header, min_width, max_width) in enumerate(zip(headers, min_widths, max_widths), 1):
                col_letter = worksheet.cell(row=1, column=col_idx).column_letter

                # 从表头长度开始
                max_length = len(str(header))

                # 检查数据行的内容长度
                for row_idx, data in enumerate(summary_data, 2):
                    if col_idx == 1:  # 运营编码
                        content = str(data['base_operation_code'])
                    elif col_idx == 2:  # 店铺名称
                        content = str(data.get('shop_name', '未知店铺'))
                    elif col_idx == 3:  # 手机名称
                        content = str(data['phone_name'])
                    elif col_idx == 4:  # 数据量
                        content = str(data['data_count'])
                    elif col_idx == 5:  # 跳转链接
                        content = f"跳转到 {data['sheet_name']}"
                    else:
                        content = ""

                    # 更新最大长度（考虑中文字符占用更多空间）
                    content_length = self.calculate_display_width(content)
                    max_length = max(max_length, content_length)

                # 应用最小和最大宽度限制
                optimal_width = max(min_width, min(max_length + 2, max_width))  # +2 为内边距
                worksheet.column_dimensions[col_letter].width = optimal_width

            # 单独处理K列（手机名称汇总）
            self.adjust_k_column_width(worksheet, summary_data)

        except Exception as e:
            # 如果自动调整失败，使用默认宽度
            default_widths = {'A': 25, 'B': 18, 'C': 15, 'D': 10, 'E': 20, 'K': 15}
            for col_letter, width in default_widths.items():
                worksheet.column_dimensions[col_letter].width = width

    def generate_staff_allocation_summary(self, staff_data, output_path):
        """生成客服分配总量统计表"""
        try:
            self.emit_status("正在生成客服分配总量统计表...")

            # 创建统计数据
            summary_stats = []
            total_operations = 0
            total_data_count = 0

            for staff_name, staff_operations in staff_data.items():
                staff_total_operations = len(staff_operations)
                staff_total_data = 0
                operation_details = []

                for base_operation_code, merged_info in staff_operations.items():
                    data_count = merged_info['total_rows']
                    staff_total_data += data_count

                    # 收集运营编码详情
                    phone_names = list(merged_info['phone_names']) if merged_info['phone_names'] else ['未分组']
                    phone_display = ', '.join(sort_by_pinyin(phone_names)) if phone_names != ['未分组'] else '未分组'

                    operation_details.append({
                        'operation_code': base_operation_code,
                        'data_count': data_count,
                        'phone_names': phone_display
                    })

                # 按数据量排序
                operation_details.sort(key=lambda x: x['data_count'], reverse=True)

                summary_stats.append({
                    'staff_name': staff_name,
                    'total_operations': staff_total_operations,
                    'total_data': staff_total_data,
                    'operation_details': operation_details
                })

                total_operations += staff_total_operations
                total_data_count += staff_total_data

            # 按总数据量排序
            summary_stats.sort(key=lambda x: x['total_data'], reverse=True)

            # 创建Excel文件
            summary_file = output_path / "0.客服分配总量统计.xlsx"
            wb = Workbook()

            # 创建总览sheet
            overview_ws = wb.active
            overview_ws.title = "分配总览"
            self.create_allocation_overview_sheet(overview_ws, summary_stats, total_operations, total_data_count)

            # 创建详细统计sheet
            detail_ws = wb.create_sheet(title="详细统计")
            self.create_allocation_detail_sheet(detail_ws, summary_stats)

            # 保存文件
            wb.save(summary_file)
            self.emit_status(f"✅ 客服分配总量统计表已生成: {summary_file}")

        except Exception as e:
            self.emit_status(f"❌ 生成客服分配总量统计表失败: {str(e)}")

    def create_allocation_overview_sheet(self, worksheet, summary_stats, total_operations, total_data_count):
        """创建分配总览sheet"""
        # 设置样式
        title_font = Font(name='微软雅黑', size=16, bold=True, color='FFFFFF')
        header_font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
        data_font = Font(name='微软雅黑', size=10)
        number_font = Font(name='微软雅黑', size=10, bold=True)

        from openpyxl.styles import PatternFill, Border, Side, Alignment

        title_fill = PatternFill(start_color='2F5597', end_color='2F5597', fill_type='solid')
        header_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')
        total_fill = PatternFill(start_color='70AD47', end_color='70AD47', fill_type='solid')

        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        center_alignment = Alignment(horizontal='center', vertical='center')

        # 标题
        worksheet.merge_cells('A1:D1')
        title_cell = worksheet['A1']
        title_cell.value = "客服分配总量统计表"
        title_cell.font = title_font
        title_cell.fill = title_fill
        title_cell.alignment = center_alignment
        title_cell.border = border

        # 设置表头
        headers = ['客服姓名', '分配运营编码数', '分配评价总量', '占比']
        for col_idx, header in enumerate(headers, 1):
            cell = worksheet.cell(row=3, column=col_idx, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = center_alignment
            cell.border = border

        # 写入数据
        for row_idx, stats in enumerate(summary_stats, 4):
            # 客服姓名
            cell = worksheet.cell(row=row_idx, column=1, value=stats['staff_name'])
            cell.font = data_font
            cell.alignment = center_alignment
            cell.border = border

            # 分配运营编码数
            cell = worksheet.cell(row=row_idx, column=2, value=stats['total_operations'])
            cell.font = number_font
            cell.alignment = center_alignment
            cell.border = border

            # 分配评价总量
            cell = worksheet.cell(row=row_idx, column=3, value=stats['total_data'])
            cell.font = number_font
            cell.alignment = center_alignment
            cell.border = border

            # 占比
            percentage = round((stats['total_data'] / total_data_count) * 100, 1) if total_data_count > 0 else 0
            cell = worksheet.cell(row=row_idx, column=4, value=f"{percentage}%")
            cell.font = number_font
            cell.alignment = center_alignment
            cell.border = border

        # 总计行
        total_row = len(summary_stats) + 4

        # 总计标签
        cell = worksheet.cell(row=total_row, column=1, value="总计")
        cell.font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
        cell.fill = total_fill
        cell.alignment = center_alignment
        cell.border = border

        # 总运营编码数
        cell = worksheet.cell(row=total_row, column=2, value=total_operations)
        cell.font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
        cell.fill = total_fill
        cell.alignment = center_alignment
        cell.border = border

        # 总评价量
        cell = worksheet.cell(row=total_row, column=3, value=total_data_count)
        cell.font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
        cell.fill = total_fill
        cell.alignment = center_alignment
        cell.border = border

        # 100%
        cell = worksheet.cell(row=total_row, column=4, value="100.0%")
        cell.font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
        cell.fill = total_fill
        cell.alignment = center_alignment
        cell.border = border

        # 自动调整列宽
        self.auto_adjust_overview_column_widths(worksheet, summary_stats)

        # 设置行高
        worksheet.row_dimensions[1].height = 25
        worksheet.row_dimensions[3].height = 20

    def create_allocation_detail_sheet(self, worksheet, summary_stats):
        """创建详细统计sheet"""
        # 设置样式
        title_font = Font(name='微软雅黑', size=16, bold=True, color='FFFFFF')
        staff_header_font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
        header_font = Font(name='微软雅黑', size=10, bold=True, color='FFFFFF')
        data_font = Font(name='微软雅黑', size=9)

        from openpyxl.styles import PatternFill, Border, Side, Alignment

        title_fill = PatternFill(start_color='2F5597', end_color='2F5597', fill_type='solid')
        staff_fill = PatternFill(start_color='70AD47', end_color='70AD47', fill_type='solid')
        header_fill = PatternFill(start_color='4472C4', end_color='4472C4', fill_type='solid')

        border = Border(
            left=Side(style='thin'),
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )

        center_alignment = Alignment(horizontal='center', vertical='center')
        left_alignment = Alignment(horizontal='left', vertical='center')

        # 标题
        worksheet.merge_cells('A1:D1')
        title_cell = worksheet['A1']
        title_cell.value = "客服分配详细统计"
        title_cell.font = title_font
        title_cell.fill = title_fill
        title_cell.alignment = center_alignment
        title_cell.border = border

        current_row = 3

        for staff_stats in summary_stats:
            # 客服名称标题行
            worksheet.merge_cells(f'A{current_row}:D{current_row}')
            staff_cell = worksheet[f'A{current_row}']
            staff_cell.value = f"{staff_stats['staff_name']} (共 {staff_stats['total_operations']} 个运营编码，{staff_stats['total_data']} 条评价)"
            staff_cell.font = staff_header_font
            staff_cell.fill = staff_fill
            staff_cell.alignment = center_alignment
            staff_cell.border = border

            current_row += 1

            # 表头
            headers = ['运营编码', '评价量', '手机名称', '占该客服比例']
            for col_idx, header in enumerate(headers, 1):
                cell = worksheet.cell(row=current_row, column=col_idx, value=header)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = center_alignment
                cell.border = border

            current_row += 1

            # 运营编码详情
            for detail in staff_stats['operation_details']:
                # 运营编码
                cell = worksheet.cell(row=current_row, column=1, value=detail['operation_code'])
                cell.font = data_font
                cell.alignment = left_alignment
                cell.border = border

                # 评价量
                cell = worksheet.cell(row=current_row, column=2, value=detail['data_count'])
                cell.font = data_font
                cell.alignment = center_alignment
                cell.border = border

                # 手机名称
                cell = worksheet.cell(row=current_row, column=3, value=detail['phone_names'])
                cell.font = data_font
                cell.alignment = left_alignment
                cell.border = border

                # 占该客服比例
                percentage = round((detail['data_count'] / staff_stats['total_data']) * 100, 1) if staff_stats['total_data'] > 0 else 0
                cell = worksheet.cell(row=current_row, column=4, value=f"{percentage}%")
                cell.font = data_font
                cell.alignment = center_alignment
                cell.border = border

                current_row += 1

            # 空行分隔
            current_row += 1

        # 自动调整列宽
        self.auto_adjust_detail_column_widths(worksheet, summary_stats)

        # 设置行高
        worksheet.row_dimensions[1].height = 25

    def adjust_k_column_width(self, worksheet, summary_data):
        """调整K列（手机名称汇总）的宽度"""
        try:
            # 收集所有手机名称以计算最佳宽度
            all_phone_names = set()
            for data in summary_data:
                phone_name = data.get('phone_name', '')
                if phone_name and phone_name not in ('未知手机', '', '未分组'):
                    # 处理可能的多个手机名称（用逗号分隔）
                    if ',' in phone_name:
                        phone_names = [name.strip() for name in phone_name.split(',')]
                        # 过滤掉空字符串或无效值
                        phone_names = [name for name in phone_names if name and name not in ('未知手机', '', '未分组')]
                        all_phone_names.update(phone_names)
                    else:
                        all_phone_names.add(phone_name.strip())

            # 计算最长的手机名称长度
            max_length = len('手机名称汇总')  # 标题长度
            for phone_name in all_phone_names:
                content_length = self.calculate_display_width(phone_name)
                max_length = max(max_length, content_length)

            # 设置K列宽度，最小12，最大25
            optimal_width = max(12, min(max_length + 2, 25))
            worksheet.column_dimensions['K'].width = optimal_width

        except Exception as e:
            # 如果失败，使用默认宽度
            worksheet.column_dimensions['K'].width = 15

    def calculate_display_width(self, text):
        """计算文本的显示宽度（考虑中文字符）"""
        if not text:
            return 0

        width = 0
        for char in str(text):
            # 中文字符和全角字符占用更多空间
            if ord(char) > 127:  # 非ASCII字符
                width += 2
            else:
                width += 1

        return width

    def auto_adjust_overview_column_widths(self, worksheet, summary_stats):
        """自动调整分配总览表的列宽"""
        try:
            # 定义列标题和内容
            headers = ['客服姓名', '运营编码数量', '评价总数', '平均每编码评价数']

            # 计算每列的最佳宽度
            for col_idx, header in enumerate(headers, 1):
                col_letter = chr(64 + col_idx)  # A, B, C, D

                # 从表头长度开始
                max_width = self.calculate_display_width(header)

                # 检查数据内容长度
                for stats in summary_stats:
                    if col_idx == 1:  # 客服姓名
                        content = str(stats['staff_name'])
                    elif col_idx == 2:  # 运营编码数量
                        content = str(stats['total_operations'])
                    elif col_idx == 3:  # 评价总数
                        content = str(stats['total_data'])
                    elif col_idx == 4:  # 平均每编码评价数
                        avg = stats['total_data'] / stats['total_operations'] if stats['total_operations'] > 0 else 0
                        content = f"{avg:.1f}"
                    else:
                        content = ""

                    content_width = self.calculate_display_width(content)
                    max_width = max(max_width, content_width)

                # 设置合理的列宽限制
                min_width = 8
                max_width_limit = 30
                optimal_width = max(min_width, min(max_width + 2, max_width_limit))

                worksheet.column_dimensions[col_letter].width = optimal_width

        except Exception as e:
            # 如果自动调整失败，使用默认宽度
            default_widths = {'A': 15, 'B': 18, 'C': 18, 'D': 12}
            for col_letter, width in default_widths.items():
                worksheet.column_dimensions[col_letter].width = width

    def auto_adjust_detail_column_widths(self, worksheet, summary_stats):
        """自动调整详细统计表的列宽"""
        try:
            # 定义列标题
            headers = ['运营编码', '评价数量', '店铺名称', '手机名称']

            # 计算每列的最佳宽度
            max_widths = [0, 0, 0, 0]

            # 检查表头长度
            for i, header in enumerate(headers):
                max_widths[i] = self.calculate_display_width(header)

            # 检查所有数据内容长度
            for staff_stats in summary_stats:
                for detail in staff_stats['operation_details']:
                    # 运营编码
                    content_width = self.calculate_display_width(str(detail['operation_code']))
                    max_widths[0] = max(max_widths[0], content_width)

                    # 评价数量
                    content_width = self.calculate_display_width(str(detail['data_count']))
                    max_widths[1] = max(max_widths[1], content_width)

                    # 店铺名称
                    content_width = self.calculate_display_width(str(detail.get('shop_name', '未知店铺')))
                    max_widths[2] = max(max_widths[2], content_width)

                    # 手机名称
                    content_width = self.calculate_display_width(str(detail.get('phone_name', '未知手机')))
                    max_widths[3] = max(max_widths[3], content_width)

            # 应用列宽
            col_letters = ['A', 'B', 'C', 'D']
            min_widths = [20, 10, 15, 12]  # 每列的最小宽度
            max_width_limits = [35, 15, 30, 20]  # 每列的最大宽度

            for i, col_letter in enumerate(col_letters):
                optimal_width = max(min_widths[i], min(max_widths[i] + 2, max_width_limits[i]))
                worksheet.column_dimensions[col_letter].width = optimal_width

        except Exception as e:
            # 如果自动调整失败，使用默认宽度
            default_widths = {'A': 30, 'B': 12, 'C': 25, 'D': 15}
            for col_letter, width in default_widths.items():
                worksheet.column_dimensions[col_letter].width = width

    def extract_digits_from_code(self, code):
        """
        从运营编码中提取纯数字部分

        对于带后缀的编码（如 ZDDLLS-3740581615068381598-B1），
        只提取主体部分的数字（3740581615068381598），
        不包括后缀中的数字（B1中的1）
        """
        import re
        if not code:
            return None

        code = str(code).strip()

        # 尝试匹配标准运营编码格式：PREFIX-MAINCODE-SUFFIX
        # 其中 SUFFIX 可能是 A1, B1, C2 等字母+数字组合
        match = re.match(r'^([A-Z]+)-([A-Z0-9]+)-([A-Z][0-9]*)$', code)
        if match:
            # 如果匹配到标准格式，只从主体部分提取数字
            main_code = match.group(2)
            digits = re.findall(r'\d', main_code)
            result = ''.join(digits)
            return result if result else None

        # 尝试匹配无后缀格式：PREFIX-MAINCODE
        match = re.match(r'^([A-Z]+)-([A-Z0-9]+)$', code)
        if match:
            # 如果匹配到无后缀格式，从主体部分提取数字
            main_code = match.group(2)
            digits = re.findall(r'\d', main_code)
            result = ''.join(digits)
            return result if result else None

        # 如果不匹配标准格式，回退到原来的逻辑（查找第一个连续数字序列）
        match = re.search(r'\d+', code)
        if match:
            return match.group()
        return None
