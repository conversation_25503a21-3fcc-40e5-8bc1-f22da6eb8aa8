# 拼音排序功能说明

## 功能概述
为评价拆分工具添加了中文拼音A-Z排序功能，确保分配汇总中的店铺名称和手机名称都按照拼音顺序排列。

## 修改内容

### 1. 添加依赖库
**文件**: `requirements.txt`
- 添加了 `pypinyin>=0.50.0` 依赖

### 2. 新增拼音排序工具函数
**文件**: `utils/helpers.py`

新增了两个函数：
- `get_pinyin_sort_key(text)`: 获取中文文本的拼音排序键
- `sort_by_pinyin(items, key_func=None)`: 按拼音对中文文本列表进行A-Z排序

特性：
- 支持中英文混合文本
- 自动处理声调，统一转为小写
- 如果pypinyin库不可用，会降级为普通文本排序
- 支持复杂对象排序（通过key_func参数）

### 3. 修改Excel生成器
**文件**: `core/excel_generator.py`

修改了以下位置的排序逻辑：

1. **第14行**: 添加了 `sort_by_pinyin` 导入
2. **第160行**: 汇总数据中手机名称显示排序
3. **第563行**: 分配汇总表按店铺名称排序
4. **第628行**: K列手机名称汇总排序
5. **第859行**: 分配手机汇总列排序
6. **第1424行**: 客服分配统计中手机名称排序

### 4. 修改辅助函数
**文件**: `utils/helpers.py`
- **第219行**: `merge_summary_data` 函数中的手机名称排序

## 功能效果

### 分配汇总表排序
- **店铺名称列**: 按拼音A-Z排序
- **手机名称列**: 按拼音A-Z排序
- **K列手机名称汇总**: 按拼音A-Z排序

### 示例排序结果
**店铺名称排序**:
```
阿里巴巴 → 百度商店 → 抖音GALAKU成人 → 京东旗舰店 → 拼多多商城 → 淘宝小店 → 腾讯游戏 → 微信小程序 → 字节跳动
```

**手机名称排序**:
```
陈七OPPO → 李四iPhone → 刘八vivo → 王五小米 → 张三的手机 → 赵六华为 → 周九三星
```

## 兼容性
- 如果系统中没有安装pypinyin库，会自动降级为普通字符串排序
- 不会影响现有功能的正常运行
- 支持中英文混合文本的排序

## 安装说明
确保安装了pypinyin库：
```bash
pip install pypinyin>=0.50.0
```

## 技术细节
- 使用pypinyin库的 `lazy_pinyin` 函数获取拼音
- 使用 `Style.NORMAL` 样式去除声调
- 转换为小写确保排序一致性
- 支持通过key_func参数对复杂对象进行排序
